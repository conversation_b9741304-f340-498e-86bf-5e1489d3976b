<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\app\src\main\res"><file name="fade_in" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="fade_out" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\anim\fade_out.xml" qualifiers="" type="anim"/><file name="slide_up" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\anim\slide_up.xml" qualifiers="" type="anim"/><file name="ic_building" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\drawable\ic_building.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="splash_background" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\drawable\splash_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary_color">#667eea</color><color name="primary_variant">#764ba2</color><color name="primary_dark">#4c63d2</color><color name="primary_light">#8fa5ff</color><color name="secondary_color">#03DAC5</color><color name="secondary_variant">#018786</color><color name="secondary_dark">#00a693</color><color name="secondary_light">#64fff7</color><color name="background_color">#f5f5f5</color><color name="surface_color">#ffffff</color><color name="card_background">#ffffff</color><color name="text_primary">#333333</color><color name="text_secondary">#666666</color><color name="text_hint">#999999</color><color name="text_on_primary">#ffffff</color><color name="text_on_secondary">#000000</color><color name="success_color">#51cf66</color><color name="success_dark">#40c057</color><color name="error_color">#ff6b6b</color><color name="error_dark">#ee5a52</color><color name="warning_color">#ffd43b</color><color name="warning_dark">#fab005</color><color name="info_color">#74c0fc</color><color name="info_dark">#339af0</color><color name="paid_color">#51cf66</color><color name="unpaid_color">#ff6b6b</color><color name="partial_color">#ffd43b</color><color name="border_color">#e0e6ff</color><color name="divider_color">#f0f0f0</color><color name="gradient_start">#667eea</color><color name="gradient_end">#764ba2</color><color name="transparent">#00000000</color><color name="semi_transparent_black">#80000000</color><color name="semi_transparent_white">#80ffffff</color><color name="material_blue">#2196F3</color><color name="material_green">#4CAF50</color><color name="material_red">#F44336</color><color name="material_orange">#FF9800</color><color name="material_purple">#9C27B0</color><color name="dark_background">#121212</color><color name="dark_surface">#1e1e1e</color><color name="dark_text_primary">#ffffff</color><color name="dark_text_secondary">#cccccc</color></file><file path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="margin_tiny">4dp</dimen><dimen name="margin_small">8dp</dimen><dimen name="margin_medium">16dp</dimen><dimen name="margin_large">24dp</dimen><dimen name="margin_xlarge">32dp</dimen><dimen name="padding_tiny">4dp</dimen><dimen name="padding_small">8dp</dimen><dimen name="padding_medium">16dp</dimen><dimen name="padding_large">24dp</dimen><dimen name="padding_xlarge">32dp</dimen><dimen name="text_size_tiny">10sp</dimen><dimen name="text_size_small">12sp</dimen><dimen name="text_size_normal">14sp</dimen><dimen name="text_size_medium">16sp</dimen><dimen name="text_size_large">18sp</dimen><dimen name="text_size_xlarge">20sp</dimen><dimen name="text_size_xxlarge">24sp</dimen><dimen name="text_size_title">28sp</dimen><dimen name="text_size_headline">32sp</dimen><dimen name="button_height">48dp</dimen><dimen name="button_height_small">36dp</dimen><dimen name="button_height_large">56dp</dimen><dimen name="button_corner_radius">8dp</dimen><dimen name="button_corner_radius_large">24dp</dimen><dimen name="card_corner_radius">12dp</dimen><dimen name="card_elevation">4dp</dimen><dimen name="card_elevation_pressed">8dp</dimen><dimen name="icon_size_tiny">16dp</dimen><dimen name="icon_size_small">20dp</dimen><dimen name="icon_size_normal">24dp</dimen><dimen name="icon_size_medium">32dp</dimen><dimen name="icon_size_large">48dp</dimen><dimen name="icon_size_xlarge">64dp</dimen><dimen name="toolbar_height">56dp</dimen><dimen name="bottom_navigation_height">56dp</dimen><dimen name="fab_size">56dp</dimen><dimen name="fab_size_mini">40dp</dimen><dimen name="webview_margin">0dp</dimen><dimen name="webview_padding">0dp</dimen><dimen name="progress_bar_size">48dp</dimen><dimen name="progress_bar_size_small">24dp</dimen><dimen name="divider_height">1dp</dimen><dimen name="divider_margin">16dp</dimen><dimen name="border_width_thin">1dp</dimen><dimen name="border_width_normal">2dp</dimen><dimen name="border_width_thick">4dp</dimen><dimen name="elevation_low">2dp</dimen><dimen name="elevation_medium">4dp</dimen><dimen name="elevation_high">8dp</dimen><dimen name="elevation_very_high">16dp</dimen><dimen name="min_touch_target">48dp</dimen><dimen name="list_item_height_small">48dp</dimen><dimen name="list_item_height_normal">56dp</dimen><dimen name="list_item_height_large">72dp</dimen><dimen name="image_size_small">32dp</dimen><dimen name="image_size_medium">48dp</dimen><dimen name="image_size_large">64dp</dimen><dimen name="image_size_xlarge">96dp</dimen><dimen name="spacing_tiny">2dp</dimen><dimen name="spacing_small">4dp</dimen><dimen name="spacing_normal">8dp</dimen><dimen name="spacing_medium">12dp</dimen><dimen name="spacing_large">16dp</dimen><dimen name="spacing_xlarge">24dp</dimen><dimen name="input_height">48dp</dimen><dimen name="input_corner_radius">8dp</dimen><dimen name="input_padding_horizontal">16dp</dimen><dimen name="input_padding_vertical">12dp</dimen></file><file path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">نظام إدارة العقارات</string><string name="app_description">نظام شامل لإدارة العقارات والمستأجرين</string><string name="splash_title">نظام إدارة العقارات</string><string name="splash_subtitle">إدارة شاملة للعقارات والمستأجرين</string><string name="loading_message">جاري تحميل التطبيق...</string><string name="error_loading">خطأ في تحميل التطبيق</string><string name="no_internet">لا يوجد اتصال بالإنترنت</string><string name="retry">إعادة المحاولة</string><string name="exit_app">هل تريد الخروج من التطبيق؟</string><string name="yes">نعم</string><string name="no">لا</string><string name="back">رجوع</string><string name="forward">تقدم</string><string name="refresh">تحديث</string><string name="share">مشاركة</string><string name="home">الرئيسية</string><string name="share_title">مشاركة البيانات</string><string name="share_text">تم إنشاء هذا التقرير باستخدام تطبيق نظام إدارة العقارات</string><string name="error_file_not_found">الملف غير موجود</string><string name="error_permission_denied">تم رفض الصلاحية</string><string name="error_unknown">خطأ غير معروف</string><string name="settings">الإعدادات</string><string name="about">حول التطبيق</string><string name="version">الإصدار</string><string name="developer">المطور</string><string name="data_saved">تم حفظ البيانات بنجاح</string><string name="data_exported">تم تصدير البيانات بنجاح</string><string name="data_imported">تم استيراد البيانات بنجاح</string></file><file path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\values\styles.xml" qualifiers=""><style name="Theme.RealEstateManager" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">#667eea</item>
        <item name="colorPrimaryVariant">#764ba2</item>
        <item name="colorOnPrimary">#ffffff</item>
        
        
        <item name="colorSecondary">#03DAC5</item>
        <item name="colorSecondaryVariant">#018786</item>
        <item name="colorOnSecondary">#000000</item>
        
        
        <item name="android:statusBarColor">#667eea</item>
        <item name="android:navigationBarColor">#667eea</item>
        
        
        <item name="android:windowBackground">#f5f5f5</item>
        
        
        <item name="android:textColorPrimary">#333333</item>
        <item name="android:textColorSecondary">#666666</item>
        
        
        <item name="android:layoutDirection">rtl</item>
        <item name="android:textDirection">rtl</item>
    </style><style name="SplashTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:statusBarColor">#667eea</item>
        <item name="android:navigationBarColor">#667eea</item>
        <item name="android:windowFullscreen">true</item>
    </style><style name="WebViewTheme" parent="Theme.RealEstateManager">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">false</item>
    </style><style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="backgroundTint">#667eea</item>
        <item name="android:textColor">#ffffff</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/cairo_bold</item>
    </style><style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">#667eea</item>
        <item name="android:textColor">#667eea</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/cairo_regular</item>
    </style><style name="TitleText">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">#333333</item>
        <item name="android:fontFamily">@font/cairo_bold</item>
        <item name="android:gravity">center</item>
    </style><style name="SubtitleText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">#666666</item>
        <item name="android:fontFamily">@font/cairo_regular</item>
        <item name="android:gravity">center</item>
    </style><style name="BodyText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">#333333</item>
        <item name="android:fontFamily">@font/cairo_regular</item>
    </style></file><file path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Adart3akar" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.Adart3akar" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\Desktop\New folder\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>