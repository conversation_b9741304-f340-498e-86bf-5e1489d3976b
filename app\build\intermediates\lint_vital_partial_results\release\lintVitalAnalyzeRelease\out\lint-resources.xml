http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/fade_in.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/slide_up.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/fade_out.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/splash_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_building.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+anim:fade_in,0,F;slide_up,1,F;fade_out,2,F;+color:dark_text_primary,3,V400400961,**********,;"#ffffff";text_primary,3,V4001402cb,2e001402f5,;"#333333";info_dark,3,V40022051f,2b00220546,;"#339af0";gradient_start,3,V4002e06a0,30002e06cc,;"#667eea";material_blue,3,V4003707ec,2f00370817,;"#2196F3";dark_surface,3,V4003f0932,2e003f095c,;"#1e1e1e";material_red,3,V40039084d,2e00390877,;"#F44336";surface_color,3,V40010024f,2f0010027a,;"#ffffff";white,3,V400490acd,2700490af0,;"#ffffff";teal_700,3,V400480aa2,2a00480ac8,;"#018786";gradient_end,3,V4002f06d1,2e002f06fb,;"#764ba2";unpaid_color,3,V40026059c,2e002605c6,;"#ff6b6b";purple_700,3,V400460a4a,2c00460a72,;"#764ba2";primary_dark,3,V4000500b5,2e000500df,;"#4c63d2";success_color,3,V4001b03d9,2f001b0404,;"#51cf66";purple_500,3,V400450a1d,2c00450a45,;"#667eea";text_on_secondary,3,V400180389,33001803b8,;"#000000";success_dark,3,V4001c0409,2e001c0433,;"#40c057";card_background,3,V40011027f,31001102ac,;"#ffffff";border_color,3,V4002a0623,2e002a064d,;"#e0e6ff";primary_color,3,V400030053,2f0003007e,;"#667eea";partial_color,3,V4002705cb,2f002705f6,;"#ffd43b";paid_color,3,V40025056f,2c00250597,;"#51cf66";semi_transparent_black,3,V400330751,3a00330787,;"#80000000";dark_text_secondary,3,V400410995,35004109c6,;"#cccccc";error_dark,3,V4001e0466,2c001e048e,;"#ee5a52";text_on_primary,3,V400170357,3100170384,;"#ffffff";error_color,3,V4001d0438,2d001d0461,;"#ff6b6b";purple_200,3,V4004409f0,2c00440a18,;"#BBDEFB";info_color,3,V4002104f2,2c0021051a,;"#74c0fc";secondary_variant,3,V4000a0165,33000a0194,;"#018786";primary_variant,3,V400040083,31000400b0,;"#764ba2";divider_color,3,V4002b0652,2f002b067d,;"#f0f0f0";transparent,3,V400320721,2f0032074c,;"#00000000";teal_200,3,V400470a77,2a00470a9d,;"#03DAC5";secondary_dark,3,V4000b0199,30000b01c5,;"#00a693";warning_color,3,V4001f0493,2f001f04be,;"#ffd43b";material_purple,3,V4003b08ae,31003b08db,;"#9C27B0";dark_background,3,V4003e0900,31003e092d,;"#121212";secondary_color,3,V400090133,3100090160,;"#03DAC5";material_orange,3,V4003a087c,31003a08a9,;"#FF9800";primary_light,3,V4000600e4,2f0006010f,;"#8fa5ff";material_green,3,V40038081c,3000380848,;"#4CAF50";black,3,V4004a0af5,27004a0b18,;"#000000";background_color,3,V4000f021c,32000f024a,;"#f5f5f5";warning_dark,3,V4002004c3,2e002004ed,;"#fab005";secondary_light,3,V4000c01ca,31000c01f7,;"#64fff7";text_hint,3,V40016032b,2b00160352,;"#999999";text_secondary,3,V4001502fa,3000150326,;"#666666";semi_transparent_white,3,V40034078c,3a003407c2,;"#80ffffff";+dimen:button_height,4,V4001b0407,2c001b042f,;"48dp";list_item_height_normal,4,V400500b8b,3600500bbd,;"56dp";input_height,4,V400620e24,2b00620e4b,;"48dp";image_size_large,4,V400560c7c,2f00560ca7,;"64dp";text_size_medium,4,V4001302c2,2f001302ed,;"16sp";bottom_navigation_height,4,V400300748,370030077b,;"56dp";border_width_thick,4,V4004309d2,30004309fe,;"4dp";button_height_large,4,V4001d0467,32001d0495,;"56dp";spacing_xlarge,4,V4005f0dd6,2d005f0dff,;"24dp";text_size_small,4,V400110263,2e0011028d,;"12sp";divider_margin,4,V4003e0923,2d003e094c,;"16dp";elevation_very_high,4,V400490aa7,3200490ad5,;"16dp";text_size_headline,4,V4001803b1,31001803de,;"32sp";padding_small,4,V4000a0163,2b000a018a,;"8dp";text_size_tiny,4,V400100235,2d0010025e,;"10sp";text_size_xlarge,4,V400150321,2f0015034c,;"20sp";toolbar_height,4,V4002f071a,2d002f0743,;"56dp";border_width_normal,4,V4004209a0,31004209cd,;"2dp";icon_size_medium,4,V4002a0667,2f002a0692,;"32dp";divider_height,4,V4003d08f6,2c003d091e,;"1dp";input_corner_radius,4,V400630e50,3100630e7d,;"8dp";padding_tiny,4,V400090138,2a0009015e,;"4dp";fab_size,4,V400310780,27003107a3,;"56dp";elevation_high,4,V400480a7a,2c00480aa2,;"8dp";margin_tiny,4,V400030058,290003007d,;"4dp";list_item_height_small,4,V4004f0b55,35004f0b86,;"48dp";spacing_normal,4,V4005c0d4e,2c005c0d76,;"8dp";spacing_small,4,V4005b0d22,2b005b0d49,;"4dp";elevation_low,4,V400460a1f,2b00460a46,;"2dp";card_elevation,4,V40023055b,2c00230583,;"4dp";image_size_xlarge,4,V400570cac,3000570cd8,;"96dp";icon_size_large,4,V4002b0697,2e002b06c1,;"48dp";text_size_xxlarge,4,V400160351,300016037d,;"24sp";elevation_medium,4,V400470a4b,2e00470a75,;"4dp";input_padding_horizontal,4,V400640e82,3700640eb5,;"16dp";card_elevation_pressed,4,V400240588,34002405b8,;"8dp";min_touch_target,4,V4004c0b01,2f004c0b2c,;"48dp";input_padding_vertical,4,V400650eba,3500650eeb,;"12dp";list_item_height_large,4,V400510bc2,3500510bf3,;"72dp";margin_small,4,V400040082,2a000400a8,;"8dp";icon_size_normal,4,V400290637,2f00290662,;"24dp";webview_margin,4,V4003507fa,2c00350822,;"0dp";icon_size_xlarge,4,V4002c06c6,2f002c06f1,;"64dp";image_size_medium,4,V400550c4b,3000550c77,;"48dp";margin_xlarge,4,V400070106,2c0007012e,;"32dp";card_corner_radius,4,V400220529,3100220556,;"12dp";icon_size_small,4,V400280608,2e00280632,;"20dp";button_corner_radius_large,4,V4001f04cd,39001f0502,;"24dp";padding_large,4,V4000c01bd,2c000c01e5,;"24dp";padding_medium,4,V4000b018f,2d000b01b8,;"16dp";border_width_thin,4,V400410970,2f0041099b,;"1dp";margin_medium,4,V4000500ad,2c000500d5,;"16dp";button_height_small,4,V4001c0434,32001c0462,;"36dp";spacing_tiny,4,V4005a0cf7,2a005a0d1d,;"2dp";image_size_small,4,V400540c1b,2f00540c46,;"32dp";text_size_normal,4,V400120292,2f001202bd,;"14sp";progress_bar_size,4,V400390874,30003908a0,;"48dp";margin_large,4,V4000600da,2b00060101,;"24dp";text_size_large,4,V4001402f2,2e0014031c,;"18sp";webview_padding,4,V400360827,2d00360850,;"0dp";padding_xlarge,4,V4000d01ea,2d000d0213,;"32dp";spacing_large,4,V4005e0da9,2c005e0dd1,;"16dp";spacing_medium,4,V4005d0d7b,2d005d0da4,;"12dp";icon_size_tiny,4,V4002705da,2d00270603,;"16dp";text_size_title,4,V400170382,2e001703ac,;"28sp";fab_size_mini,4,V4003207a8,2c003207d0,;"40dp";button_corner_radius,4,V4001e049a,32001e04c8,;"8dp";progress_bar_size_small,4,V4003a08a5,36003a08d7,;"24dp";+drawable:splash_background,5,F;ic_launcher_foreground,6,F;ic_launcher_background,7,F;ic_building,8,F;+id:subtitle_text,9,F;webview,10,F;title_text,9,F;logo_image,9,F;+layout:activity_main,10,F;activity_splash,9,F;+mipmap:ic_launcher_round,11,F;ic_launcher_round,12,F;ic_launcher_round,13,F;ic_launcher_round,14,F;ic_launcher_round,15,F;ic_launcher,16,F;ic_launcher,17,F;ic_launcher,18,F;ic_launcher,19,F;ic_launcher,20,F;+string:no,21,V4001002e1,21001002fe,;"لا";error_permission_denied,21,V4001f0507,43001f0546,;"تم رفض الصلاحية";data_exported,21,V4002a06b7,41002a06f4,;"تم تصدير البيانات بنجاح";about,21,V4002405d4,2d002405fd,;"حول التطبيق";back,21,V400130322,2500130343,;"رجوع";data_imported,21,V4002b06f9,43002b0738,;"تم استيراد البيانات بنجاح";error_loading,21,V4000b01cd,3e000b0207,;"خطأ في تحميل التطبيق";error_unknown,21,V40020054b,370020057e,;"خطأ غير معروف";share,21,V40016039b,28001603bf,;"مشاركة";retry,21,V4000d024c,30000d0278,;"إعادة المحاولة";loading_message,21,V40008016b,41000801a8,;"جاري تحميل التطبيق...";share_text,21,V4001b0447,5e001b04a1,;"تم إنشاء هذا التقرير باستخدام تطبيق نظام إدارة العقارات";settings,21,V4002305a5,2e002305cf,;"الإعدادات";splash_title,21,V4000600e1,3c00060119,;"نظام إدارة العقارات";splash_subtitle,21,V40007011e,4c00070166,;"إدارة شاملة للعقارات والمستأجرين";yes,21,V4000f02bd,23000f02dc,;"نعم";forward,21,V400140348,280014036c,;"تقدم";error_file_not_found,21,V4001e04c6,40001e0502,;"الملف غير موجود";refresh,21,V400150371,2900150396,;"تحديث";version,21,V400250602,2b00250629,;"الإصدار";home,21,V4001703c4,29001703e9,;"الرئيسية";app_name,21,V400020037,380002006b,;"نظام إدارة العقارات";data_saved,21,V40029067a,3c002906b2,;"تم حفظ البيانات بنجاح";share_title,21,V4001a040f,37001a0442,;"مشاركة البيانات";developer,21,V40026062e,2c00260656,;"المطور";app_description,21,V400030070,51000300bd,;"نظام شامل لإدارة العقارات والمستأجرين";no_internet,21,V4000c020c,3f000c0247,;"لا يوجد اتصال بالإنترنت";exit_app,21,V4000e027d,3f000e02b8,;"هل تريد الخروج من التطبيق؟";+style:TitleText,22,V4004009f6,c00450af7,;Nandroid\:textSize:24sp,android\:textColor:#333333,android\:fontFamily:sans-serif-medium,android\:gravity:center,;Theme.Adart3akar,23,V400020064,c000e031f,;DTheme.MaterialComponents.DayNight.DarkActionBar,colorPrimary:@color/purple_500,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,android\:statusBarColor:?attr/colorPrimaryVariant,;Theme.Adart3akar,24,V400020064,c000e031f,;DTheme.MaterialComponents.DayNight.DarkActionBar,colorPrimary:@color/purple_200,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/black,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_200,colorOnSecondary:@color/black,android\:statusBarColor:?attr/colorPrimaryVariant,;WebViewTheme,22,V40028060e,c002c0703,;DTheme.RealEstateManager,android\:windowNoTitle:true,android\:windowActionBar:false,android\:windowFullscreen:false,;PrimaryButton,22,V4002f0724,c00350877,;DWidget.Material3.Button,backgroundTint:#667eea,cornerRadius:8dp,android\:textColor:#ffffff,android\:textSize:16sp,android\:fontFamily:sans-serif-medium,;BodyText,22,V4004e0c00,c00520cc6,;Nandroid\:textSize:14sp,android\:textColor:#333333,android\:fontFamily:sans-serif,;SubtitleText,22,V400470afd,c004c0bfa,;Nandroid\:textSize:16sp,android\:textColor:#666666,android\:fontFamily:sans-serif,android\:gravity:center,;SecondaryButton,22,V40037087d,c003d09d7,;DWidget.Material3.Button.OutlinedButton,strokeColor:#667eea,cornerRadius:8dp,android\:textColor:#667eea,android\:textSize:16sp,android\:fontFamily:sans-serif,;Theme.RealEstateManager,22,V40004005c,c001d046c,;DTheme.Material3.DayNight.NoActionBar,colorPrimary:#667eea,colorPrimaryVariant:#764ba2,colorOnPrimary:#ffffff,colorSecondary:#03DAC5,colorSecondaryVariant:#018786,colorOnSecondary:#000000,android\:statusBarColor:#667eea,android\:navigationBarColor:#667eea,android\:windowBackground:#f5f5f5,android\:textColorPrimary:#333333,android\:textColorSecondary:#666666,android\:layoutDirection:rtl,android\:textDirection:rtl,;SplashTheme,22,V400200493,c002505ed,;DTheme.Material3.DayNight.NoActionBar,android\:windowBackground:@drawable/splash_background,android\:statusBarColor:#667eea,android\:navigationBarColor:#667eea,android\:windowFullscreen:true,;+xml:file_paths,25,F;network_security_config,26,F;data_extraction_rules,27,F;backup_rules,28,F;