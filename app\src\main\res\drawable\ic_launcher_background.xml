<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">

    <!-- Premium gradient background -->
    <path android:pathData="M0,0h108v108h-108z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="108"
                android:endY="108"
                android:startX="0"
                android:startY="0"
                android:type="radial"
                android:gradientRadius="76">
                <item
                    android:color="#667eea"
                    android:offset="0.0" />
                <item
                    android:color="#764ba2"
                    android:offset="0.7" />
                <item
                    android:color="#5a3d7a"
                    android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>

    <!-- Decorative circles -->
    <path android:pathData="M20,20 L30,20 L30,30 L20,30 Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="30"
                android:endY="30"
                android:startX="20"
                android:startY="20"
                android:type="radial"
                android:gradientRadius="7">
                <item
                    android:color="#20FFFFFF"
                    android:offset="0.0" />
                <item
                    android:color="#05FFFFFF"
                    android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>

    <path android:pathData="M78,78 L88,78 L88,88 L78,88 Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="88"
                android:endY="88"
                android:startX="78"
                android:startY="78"
                android:type="radial"
                android:gradientRadius="7">
                <item
                    android:color="#20FFFFFF"
                    android:offset="0.0" />
                <item
                    android:color="#05FFFFFF"
                    android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>

    <path android:pathData="M85,15 L95,15 L95,25 L85,25 Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="95"
                android:endY="25"
                android:startX="85"
                android:startY="15"
                android:type="radial"
                android:gradientRadius="7">
                <item
                    android:color="#15FFFFFF"
                    android:offset="0.0" />
                <item
                    android:color="#03FFFFFF"
                    android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>

    <path android:pathData="M13,83 L23,83 L23,93 L13,93 Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="23"
                android:endY="93"
                android:startX="13"
                android:startY="83"
                android:type="radial"
                android:gradientRadius="7">
                <item
                    android:color="#15FFFFFF"
                    android:offset="0.0" />
                <item
                    android:color="#03FFFFFF"
                    android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>

    <!-- Subtle geometric pattern -->
    <path
        android:fillColor="#08FFFFFF"
        android:pathData="M0,0L12,0L12,12L0,12L0,0zM24,0L36,0L36,12L24,12L24,0zM48,0L60,0L60,12L48,12L48,0zM72,0L84,0L84,12L72,12L72,0zM96,0L108,0L108,12L96,12L96,0z" />

    <path
        android:fillColor="#08FFFFFF"
        android:pathData="M0,24L12,24L12,36L0,36L0,24zM24,24L36,24L36,36L24,36L24,24zM48,24L60,24L60,36L48,36L48,24zM72,24L84,24L84,36L72,36L72,24zM96,24L108,24L108,36L96,36L96,24z" />

    <path
        android:fillColor="#08FFFFFF"
        android:pathData="M0,48L12,48L12,60L0,60L0,48zM24,48L36,48L36,60L24,60L24,48zM48,48L60,48L60,60L48,60L48,48zM72,48L84,48L84,60L72,60L72,48zM96,48L108,48L108,60L96,60L96,48z" />

    <path
        android:fillColor="#08FFFFFF"
        android:pathData="M0,72L12,72L12,84L0,84L0,72zM24,72L36,72L36,84L24,84L24,72zM48,72L60,72L60,84L48,84L48,72zM72,72L84,72L84,84L72,84L72,72zM96,72L108,72L108,84L96,84L96,72z" />

    <path
        android:fillColor="#08FFFFFF"
        android:pathData="M0,96L12,96L12,108L0,108L0,96zM24,96L36,96L36,108L24,108L24,96zM48,96L60,96L60,108L48,108L48,96zM72,96L84,96L84,108L72,108L72,96zM96,96L108,96L108,108L96,108L96,96z" />

</vector>
