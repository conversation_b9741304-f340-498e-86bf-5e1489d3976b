1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lawoffice.adart3akar"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <!-- الصلاحيات المطلوبة -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:7:5-67
12-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:7:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:8:5-79
13-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:8:22-76
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:9:5-81
14-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:9:22-78
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:10:5-80
15-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:10:22-77
16    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
16-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:11:5-88
16-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:11:22-85
17
18    <permission
18-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.lawoffice.adart3akar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.lawoffice.adart3akar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:13:5-58:19
25        android:allowBackup="true"
25-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:14:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:15:9-65
28        android:debuggable="true"
29        android:extractNativeLibs="true"
30        android:fullBackupContent="@xml/backup_rules"
30-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:16:9-54
31        android:icon="@mipmap/ic_launcher"
31-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:17:9-43
32        android:label="@string/app_name"
32-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:18:9-41
33        android:networkSecurityConfig="@xml/network_security_config"
33-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:23:9-69
34        android:roundIcon="@mipmap/ic_launcher_round"
34-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:19:9-54
35        android:supportsRtl="true"
35-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:20:9-35
36        android:theme="@style/Theme.RealEstateManager"
36-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:21:9-55
37        android:usesCleartextTraffic="true" >
37-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:22:9-44
38
39        <!-- النشاط الرئيسي -->
40        <activity
40-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:27:9-38:20
41            android:name="com.lawoffice.adart3akar.MainActivity"
41-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:28:13-41
42            android:configChanges="orientation|screenSize|keyboardHidden"
42-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:31:13-74
43            android:exported="true"
43-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:29:13-36
44            android:screenOrientation="portrait"
44-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:30:13-49
45            android:windowSoftInputMode="adjustResize" >
45-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:32:13-55
46            <intent-filter>
46-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:34:13-37:29
47                <action android:name="android.intent.action.MAIN" />
47-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:35:17-69
47-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:35:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:36:17-77
49-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:36:27-74
50            </intent-filter>
51        </activity>
52
53        <!-- نشاط شاشة البداية (اختياري) -->
54        <activity
54-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:41:9-45:52
55            android:name="com.lawoffice.adart3akar.SplashActivity"
55-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:42:13-43
56            android:exported="false"
56-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:43:13-37
57            android:screenOrientation="portrait"
57-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:45:13-49
58            android:theme="@style/SplashTheme" />
58-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:44:13-47
59
60        <!-- File Provider للمشاركة -->
61        <provider
62            android:name="androidx.core.content.FileProvider"
62-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:49:13-62
63            android:authorities="com.lawoffice.adart3akar.fileprovider"
63-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:50:13-64
64            android:exported="false"
64-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:51:13-37
65            android:grantUriPermissions="true" >
65-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:52:13-47
66            <meta-data
66-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:53:13-55:54
67                android:name="android.support.FILE_PROVIDER_PATHS"
67-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:54:17-67
68                android:resource="@xml/file_paths" />
68-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:55:17-51
69        </provider>
70        <provider
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
71            android:name="androidx.startup.InitializationProvider"
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
72            android:authorities="com.lawoffice.adart3akar.androidx-startup"
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
73            android:exported="false" >
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
74            <meta-data
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.emoji2.text.EmojiCompatInitializer"
75-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
76                android:value="androidx.startup" />
76-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eefc95c541adda1444a74349370ce8b1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
78-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eefc95c541adda1444a74349370ce8b1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
79                android:value="androidx.startup" />
79-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eefc95c541adda1444a74349370ce8b1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
80            <meta-data
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
81                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
82                android:value="androidx.startup" />
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
83        </provider>
84
85        <uses-library
85-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
86            android:name="androidx.window.extensions"
86-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
87            android:required="false" />
87-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
88        <uses-library
88-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
89            android:name="androidx.window.sidecar"
89-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
90            android:required="false" />
90-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
91
92        <receiver
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
93            android:name="androidx.profileinstaller.ProfileInstallReceiver"
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
94            android:directBootAware="false"
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
95            android:enabled="true"
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
96            android:exported="true"
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
97            android:permission="android.permission.DUMP" >
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
99                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
100            </intent-filter>
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
102                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
103            </intent-filter>
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
105                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
108                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
109            </intent-filter>
110        </receiver>
111    </application>
112
113</manifest>
