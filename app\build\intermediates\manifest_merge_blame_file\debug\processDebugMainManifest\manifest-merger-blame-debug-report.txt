1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lawoffice.adart3akar"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <!-- الصلاحيات المطلوبة -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:9:5-80
15-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:9:22-77
16    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
16-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:10:5-88
16-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:10:22-85
17
18    <permission
18-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.lawoffice.adart3akar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.lawoffice.adart3akar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:12:5-52:19
25        android:allowBackup="true"
25-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:13:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:14:9-65
28        android:debuggable="true"
29        android:extractNativeLibs="true"
30        android:fullBackupContent="@xml/backup_rules"
30-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:15:9-54
31        android:icon="@mipmap/ic_launcher"
31-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:16:9-43
32        android:label="@string/app_name"
32-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:17:9-41
33        android:networkSecurityConfig="@xml/network_security_config"
33-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:22:9-69
34        android:roundIcon="@mipmap/ic_launcher_round"
34-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:18:9-54
35        android:supportsRtl="true"
35-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:19:9-35
36        android:theme="@style/Theme.RealEstateManager"
36-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:20:9-55
37        android:usesCleartextTraffic="true" >
37-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:21:9-44
38
39        <!-- النشاط الرئيسي -->
40        <activity
40-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:26:9-37:20
41            android:name="com.lawoffice.adart3akar.MainActivity"
41-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:27:13-41
42            android:configChanges="orientation|screenSize|keyboardHidden"
42-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:30:13-74
43            android:exported="true"
43-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:28:13-36
44            android:screenOrientation="portrait"
44-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:29:13-49
45            android:windowSoftInputMode="adjustResize" >
45-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:31:13-55
46            <intent-filter>
46-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:33:13-36:29
47                <action android:name="android.intent.action.MAIN" />
47-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:34:17-69
47-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:34:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:35:17-77
49-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:35:27-74
50            </intent-filter>
51        </activity>
52
53        <!-- File Provider للمشاركة -->
54        <provider
55            android:name="androidx.core.content.FileProvider"
55-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:43:13-62
56            android:authorities="com.lawoffice.adart3akar.fileprovider"
56-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:44:13-64
57            android:exported="false"
57-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:45:13-37
58            android:grantUriPermissions="true" >
58-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:46:13-47
59            <meta-data
59-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:47:13-49:54
60                android:name="android.support.FILE_PROVIDER_PATHS"
60-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:48:17-67
61                android:resource="@xml/file_paths" />
61-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:49:17-51
62        </provider>
63        <provider
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
64            android:name="androidx.startup.InitializationProvider"
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
65            android:authorities="com.lawoffice.adart3akar.androidx-startup"
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
66            android:exported="false" >
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
67            <meta-data
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
68                android:name="androidx.emoji2.text.EmojiCompatInitializer"
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
69                android:value="androidx.startup" />
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
70            <meta-data
70-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eefc95c541adda1444a74349370ce8b1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
71                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
71-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eefc95c541adda1444a74349370ce8b1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
72                android:value="androidx.startup" />
72-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eefc95c541adda1444a74349370ce8b1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
73            <meta-data
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
75                android:value="androidx.startup" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
76        </provider>
77
78        <uses-library
78-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
79            android:name="androidx.window.extensions"
79-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
80            android:required="false" />
80-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
81        <uses-library
81-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
82            android:name="androidx.window.sidecar"
82-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
83            android:required="false" />
83-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
84
85        <receiver
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
86            android:name="androidx.profileinstaller.ProfileInstallReceiver"
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
87            android:directBootAware="false"
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
88            android:enabled="true"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
89            android:exported="true"
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
90            android:permission="android.permission.DUMP" >
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
92                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
95                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
98                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
99            </intent-filter>
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
101                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
102            </intent-filter>
103        </receiver>
104    </application>
105
106</manifest>
