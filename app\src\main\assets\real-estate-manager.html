<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العقارات المتقدم</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- PDF and Excel Export Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        /* Main Content */
        .main-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        /* Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
        }

        .stat-card p {
            opacity: 0.9;
        }

        /* Add Property Section */
        .add-property-section {
            background: #f8f9ff;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border: 2px solid #e0e6ff;
        }

        .section-title {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e6ff;
            border-radius: 10px;
            font-family: inherit;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        /* Tenants List */
        .tenants-container {
            margin-top: 20px;
            display: none;
        }

        .tenant-item {
            background: white;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .tenant-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .tenant-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        /* Buttons */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #51cf66, #40c057);
            color: white;
            box-shadow: 0 4px 15px rgba(81, 207, 102, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(81, 207, 102, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        /* Properties List */
        .properties-list {
            margin-top: 30px;
        }

        .property-card {
            background: white;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .property-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .property-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .property-title {
            font-size: 1.5rem;
            color: #333;
            font-weight: 700;
        }

        .property-actions {
            display: flex;
            gap: 10px;
        }

        .tenants-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .tenant-card {
            background: #f8f9ff;
            border: 1px solid #e0e6ff;
            border-radius: 10px;
            padding: 15px;
        }

        .tenant-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .tenant-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9rem;
            color: #666;
        }

        .status-paid {
            color: #51cf66;
            font-weight: 600;
        }

        .status-unpaid {
            color: #ff6b6b;
            font-weight: 600;
        }

        .status-partial {
            color: #ffd43b;
            font-weight: 600;
        }

        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 1000;
            transform: translateX(400px);
            opacity: 0;
            transition: all 0.3s ease;
            min-width: 300px;
            border-left: 4px solid #667eea;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification-success {
            border-left-color: #51cf66;
            color: #2b8a3e;
        }

        .notification-error {
            border-left-color: #ff6b6b;
            color: #c92a2a;
        }

        .notification-info {
            border-left-color: #339af0;
            color: #1864ab;
        }

        /* Enhanced Mobile Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 1.5rem;
                text-align: center;
            }

            .header p {
                font-size: 0.9rem;
                text-align: center;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .tenant-fields {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .tenants-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .tenant-info {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .property-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
                text-align: center;
            }

            .property-actions {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
                width: 100%;
            }

            .tenant-actions {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
                margin-top: 15px;
            }

            .reports-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .report-controls {
                gap: 10px;
            }

            .report-controls .btn {
                font-size: 0.85rem;
                padding: 10px 12px;
                width: 100%;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .stat-card {
                padding: 15px;
                text-align: center;
            }

            .stat-card h3 {
                font-size: 1.5rem;
            }

            .data-buttons {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }

            .btn {
                font-size: 0.85rem;
                padding: 12px 15px;
                text-align: center;
                justify-content: center;
            }

            .property-card {
                padding: 15px;
                margin-bottom: 15px;
            }

            .tenant-card {
                padding: 12px;
                margin-bottom: 10px;
            }

            .tenant-name {
                font-size: 1rem;
                text-align: center;
                margin-bottom: 12px;
            }

            /* تحسين الجداول للهاتف */
            .report-table {
                font-size: 0.8rem;
                overflow-x: auto;
                display: block;
                white-space: nowrap;
            }

            .report-table th,
            .report-table td {
                padding: 8px 6px;
                min-width: 80px;
            }

            /* تحسين النماذج للهاتف */
            .form-group input,
            .form-group select,
            .form-group textarea {
                font-size: 16px; /* منع التكبير في iOS */
                padding: 12px;
            }

            /* تحسين الإشعارات للهاتف */
            .notification {
                right: 10px;
                left: 10px;
                top: 10px;
                min-width: auto;
                transform: translateY(-100px);
            }

            .notification.show {
                transform: translateY(0);
            }
        }

        /* تحسينات إضافية للشاشات الصغيرة جداً */
        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .property-actions,
            .tenant-actions {
                grid-template-columns: 1fr;
            }

            .data-buttons {
                grid-template-columns: 1fr;
            }

            .btn {
                padding: 15px;
                font-size: 0.9rem;
            }

            .property-title {
                font-size: 1.2rem;
                text-align: center;
            }

            .tenant-info {
                text-align: center;
            }

            .tenant-info > div {
                padding: 5px 0;
                border-bottom: 1px solid #eee;
            }

            .tenant-info > div:last-child {
                border-bottom: none;
            }
        }

        /* Export/Import Section */
        .data-management {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .data-management h3 {
            color: #856404;
            margin-bottom: 15px;
        }

        .data-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffd43b, #fab005);
            color: #856404;
            box-shadow: 0 4px 15px rgba(255, 212, 59, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #74c0fc, #339af0);
            color: white;
            box-shadow: 0 4px 15px rgba(116, 192, 252, 0.3);
        }

        #fileInput {
            display: none;
        }

        /* Reports Section */
        .reports-section {
            background: #f8f9ff;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border: 2px solid #e0e6ff;
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .report-card {
            background: white;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .report-card h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .report-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .report-controls .btn {
            font-size: 0.9rem;
            padding: 8px 12px;
        }

        .btn-pdf {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .btn-excel {
            background: linear-gradient(135deg, #28a745, #218838);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .report-controls select {
            padding: 8px 12px;
            border: 2px solid #e0e6ff;
            border-radius: 8px;
            font-family: inherit;
        }

        .report-display {
            background: white;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .report-content {
            line-height: 1.6;
        }

        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .report-table th,
        .report-table td {
            padding: 12px;
            text-align: right;
            border: 1px solid #e0e6ff;
        }

        .report-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }

        .report-table tr:nth-child(even) {
            background: #f8f9ff;
        }

        .report-summary {
            background: #f8f9ff;
            border: 2px solid #e0e6ff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .summary-item:last-child {
            margin-bottom: 0;
            border-top: 2px solid #667eea;
            padding-top: 10px;
            color: #667eea;
            font-size: 1.1rem;
        }

        .expense-item {
            color: #ff6b6b;
        }

        .income-item {
            color: #51cf66;
        }

        .net-item {
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-building"></i> نظام إدارة العقارات المتقدم</h1>
            <p>إدارة شاملة للعقارات والمستأجرين مع حفظ البيانات المحمول</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-building"></i>
                <h3 id="totalProperties">0</h3>
                <p>إجمالي العقارات</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-users"></i>
                <h3 id="totalTenants">0</h3>
                <p>إجمالي المستأجرين</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-dollar-sign"></i>
                <h3 id="totalIncome">0 ج.م</h3>
                <p>صافي الدخل الشهري</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-check-circle"></i>
                <h3 id="paidTenants">0</h3>
                <p>المستأجرين المدفوعين</p>
            </div>
        </div>

        <!-- Data Management -->
        <div class="data-management">
            <h3><i class="fas fa-database"></i> إدارة البيانات</h3>
            <div class="data-buttons">
                <button class="btn btn-info" onclick="exportData()">
                    <i class="fas fa-download"></i> تصدير البيانات
                </button>
                <button class="btn btn-success" onclick="exportDataForAndroid()">
                    <i class="fas fa-share"></i> مشاركة البيانات
                </button>
                <button class="btn btn-warning" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-upload"></i> استيراد البيانات
                </button>
                <button class="btn btn-danger" onclick="clearAllData()">
                    <i class="fas fa-trash"></i> مسح جميع البيانات
                </button>
            </div>
            <input type="file" id="fileInput" accept=".json" onchange="importData(event)">
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Add Property Form -->
            <div class="add-property-section">
                <h2 class="section-title">
                    <i class="fas fa-plus-circle"></i> إضافة عقار جديد
                </h2>

                <form id="propertyForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="propertyName">اسم العقار</label>
                            <input type="text" id="propertyName" required placeholder="أدخل اسم العقار">
                        </div>
                        <div class="form-group">
                            <label for="tenantCount">عدد المستأجرين</label>
                            <input type="number" id="tenantCount" min="1" max="50" required placeholder="عدد المستأجرين">
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="button" class="btn btn-primary" onclick="generateTenantFields()">
                            <i class="fas fa-users"></i> إنشاء قوائم المستأجرين
                        </button>
                    </div>

                    <!-- Tenants Container -->
                    <div id="tenantsContainer" class="tenants-container"></div>

                    <div class="form-group" id="saveButtonContainer" style="display: none;">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> حفظ العقار والمستأجرين
                        </button>
                    </div>
                </form>
            </div>

            <!-- Reports Section -->
            <div class="reports-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-bar"></i> التقارير والإحصائيات
                </h2>

                <div class="reports-grid">
                    <div class="report-card">
                        <h3><i class="fas fa-calendar-alt"></i> التقارير الشهرية</h3>
                        <div style="margin-bottom: 10px; text-align: center;">
                            <button class="btn btn-secondary" onclick="populateReportSelects()" style="font-size: 0.8rem; padding: 5px 10px;">
                                <i class="fas fa-sync-alt"></i> تحديث القوائم
                            </button>
                        </div>
                        <div class="report-controls">
                            <select id="reportMonth">
                                <option value="">اختر الشهر</option>
                                <option value="01">يناير</option>
                                <option value="02">فبراير</option>
                                <option value="03">مارس</option>
                                <option value="04">أبريل</option>
                                <option value="05">مايو</option>
                                <option value="06">يونيو</option>
                                <option value="07">يوليو</option>
                                <option value="08">أغسطس</option>
                                <option value="09">سبتمبر</option>
                                <option value="10">أكتوبر</option>
                                <option value="11">نوفمبر</option>
                                <option value="12">ديسمبر</option>
                            </select>
                            <select id="reportYear">
                                <option value="">اختر السنة</option>
                            </select>
                            <button class="btn btn-info" onclick="generateMonthlyReport()">
                                <i class="fas fa-file-alt"></i> إنشاء التقرير
                            </button>
                            <button class="btn btn-success" onclick="exportMonthlyReport()">
                                <i class="fas fa-download"></i> رفع JSON
                            </button>
                            <button class="btn btn-danger" onclick="exportMonthlyReportPDF()">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                            <button class="btn btn-info" onclick="exportMonthlyReportExcel()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                    </div>

                    <div class="report-card">
                        <h3><i class="fas fa-user-chart"></i> تقارير المستأجرين</h3>
                        <div class="report-controls">
                            <select id="tenantSelect">
                                <option value="">اختر المستأجر</option>
                            </select>
                            <select id="tenantReportYear">
                                <option value="">اختر السنة</option>
                            </select>
                            <button class="btn btn-info" onclick="generateTenantReport()">
                                <i class="fas fa-user"></i> تقرير المستأجر
                            </button>
                            <button class="btn btn-danger" onclick="exportTenantReportPDF()">
                                <i class="fas fa-file-pdf"></i> PDF
                            </button>
                            <button class="btn btn-success" onclick="exportTenantReportExcel()">
                                <i class="fas fa-file-excel"></i> Excel
                            </button>
                        </div>
                    </div>

                    <div class="report-card">
                        <h3><i class="fas fa-building"></i> تقارير العقارات</h3>
                        <div class="report-controls">
                            <select id="propertySelect">
                                <option value="">اختر العقار</option>
                            </select>
                            <button class="btn btn-info" onclick="generatePropertyReport()">
                                <i class="fas fa-chart-line"></i> تقرير العقار
                            </button>
                            <button class="btn btn-danger" onclick="exportPropertyReportPDF()">
                                <i class="fas fa-file-pdf"></i> PDF
                            </button>
                            <button class="btn btn-success" onclick="exportPropertyReportExcel()">
                                <i class="fas fa-file-excel"></i> Excel
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Report Display Area -->
                <div id="reportDisplay" class="report-display" style="display: none;">
                    <div class="report-header">
                        <h3 id="reportTitle"></h3>
                        <button class="btn btn-secondary" onclick="closeReport()">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                    <div id="reportContent"></div>
                </div>
            </div>

            <!-- Properties List -->
            <div class="properties-list">
                <h2 class="section-title">
                    <i class="fas fa-list"></i> قائمة العقارات
                </h2>
                <div id="propertiesList"></div>
            </div>
        </div>
    </div>

    <script>
        // Global Variables
        let properties = [];
        let currentPropertyId = null;

        // Initialize App
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            updateStatistics();
            displayProperties();
            populateReportSelects();

            // Setup form submission
            document.getElementById('propertyForm').addEventListener('submit', saveProperty);
        });

        // Generate Tenant Fields
        function generateTenantFields() {
            const propertyName = document.getElementById('propertyName').value.trim();
            const tenantCount = parseInt(document.getElementById('tenantCount').value);

            if (!propertyName) {
                showNotification('يرجى إدخال اسم العقار أولاً', 'error');
                return;
            }

            if (!tenantCount || tenantCount < 1 || tenantCount > 50) {
                showNotification('يرجى إدخال عدد صحيح للمستأجرين (1-50)', 'error');
                return;
            }

            const container = document.getElementById('tenantsContainer');
            container.innerHTML = '';
            container.style.display = 'block';

            for (let i = 1; i <= tenantCount; i++) {
                const tenantDiv = document.createElement('div');
                tenantDiv.className = 'tenant-item';
                tenantDiv.innerHTML = `
                    <div class="tenant-header">
                        <i class="fas fa-user"></i> المستأجر رقم ${i}
                    </div>
                    <div class="tenant-fields">
                        <div class="form-group">
                            <label>اسم المستأجر</label>
                            <input type="text" name="tenantName_${i}" placeholder="اسم المستأجر" required>
                        </div>
                        <div class="form-group">
                            <label>مبلغ الإيجار (ج.م)</label>
                            <input type="number" name="rentAmount_${i}" placeholder="0" min="0" step="0.01">
                        </div>
                        <div class="form-group">
                            <label>حالة الدفع</label>
                            <select name="paymentStatus_${i}">
                                <option value="">اختر الحالة</option>
                                <option value="paid">مدفوع</option>
                                <option value="unpaid">غير مدفوع</option>
                                <option value="partial">مدفوع جزئياً</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>تاريخ الدفع الأخير</label>
                            <input type="date" name="paymentDate_${i}">
                            <small style="color: #666; font-size: 0.8rem;">
                                هذا التاريخ يحدد آخر شهر تم الدفع فيه
                            </small>
                        </div>
                        <div class="form-group">
                            <label>رقم التليفون</label>
                            <input type="tel" name="phone_${i}" placeholder="05xxxxxxxx">
                        </div>
                        <div class="form-group">
                            <label>ملاحظات</label>
                            <textarea name="notes_${i}" placeholder="ملاحظات إضافية" rows="2"></textarea>
                        </div>
                    </div>
                `;
                container.appendChild(tenantDiv);
            }

            document.getElementById('saveButtonContainer').style.display = 'block';
            showNotification(`تم إنشاء ${tenantCount} قائمة للمستأجرين`, 'success');
        }

        // Save Property
        function saveProperty(e) {
            e.preventDefault();

            const propertyName = document.getElementById('propertyName').value.trim();
            const tenantCount = parseInt(document.getElementById('tenantCount').value);

            if (!propertyName || !tenantCount) {
                showNotification('يرجى ملء جميع البيانات المطلوبة', 'error');
                return;
            }

            const tenants = [];
            let totalIncome = 0;

            // Collect tenant data
            for (let i = 1; i <= tenantCount; i++) {
                const tenantName = document.querySelector(`input[name="tenantName_${i}"]`).value.trim();
                const rentAmount = parseFloat(document.querySelector(`input[name="rentAmount_${i}"]`).value) || 0;
                const paymentStatus = document.querySelector(`select[name="paymentStatus_${i}"]`).value;
                const paymentDate = document.querySelector(`input[name="paymentDate_${i}"]`).value;
                const phone = document.querySelector(`input[name="phone_${i}"]`).value.trim();
                const notes = document.querySelector(`textarea[name="notes_${i}"]`).value.trim();

                // Allow empty data - only require tenant name if any data is entered
                if (tenantName || rentAmount > 0 || paymentStatus || paymentDate || phone || notes) {
                    const tenant = {
                        id: generateId(),
                        name: tenantName || `مستأجر ${i}`,
                        rentAmount: rentAmount,
                        paymentStatus: paymentStatus || 'unpaid',
                        paymentDate: paymentDate,
                        phone: phone,
                        notes: notes,
                        paymentHistory: [], // سجل المدفوعات الجديد
                        expenseHistory: [], // سجل المصروفات الجديد
                        createdAt: new Date().toISOString()
                    };

                    // إضافة الدفعة الأولى إلى سجل المدفوعات إذا كانت موجودة
                    if (paymentDate && paymentStatus && paymentStatus !== 'unpaid') {
                        tenant.paymentHistory.push({
                            id: generateId(),
                            date: paymentDate,
                            amount: paymentStatus === 'paid' ? rentAmount : rentAmount * 0.5,
                            status: paymentStatus,
                            notes: `دفعة أولية - ${notes || ''}`,
                            createdAt: new Date().toISOString()
                        });
                    }

                    tenants.push(tenant);

                    if (paymentStatus === 'paid') {
                        totalIncome += rentAmount;
                    } else if (paymentStatus === 'partial') {
                        totalIncome += rentAmount * 0.5; // Assume 50% for partial payment
                    }
                } else {
                    // Add empty tenant slot for future completion
                    tenants.push({
                        id: generateId(),
                        name: `مستأجر ${i}`,
                        rentAmount: 0,
                        paymentStatus: 'unpaid',
                        paymentDate: '',
                        phone: '',
                        notes: '',
                        paymentHistory: [], // سجل المدفوعات فارغ
                        expenseHistory: [], // سجل المصروفات فارغ
                        isEmpty: true,
                        createdAt: new Date().toISOString()
                    });
                }
            }

            // Create property object
            const property = {
                id: generateId(),
                name: propertyName,
                tenantCount: tenantCount,
                tenants: tenants,
                totalIncome: totalIncome,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Add to properties array
            properties.push(property);

            // Save to localStorage
            saveData();

            // Update UI
            updateStatistics();
            displayProperties();
            populateReportSelects(); // تحديث قوائم التقارير

            // Reset form
            document.getElementById('propertyForm').reset();
            document.getElementById('tenantsContainer').style.display = 'none';
            document.getElementById('saveButtonContainer').style.display = 'none';

            showNotification(`تم حفظ العقار "${propertyName}" مع ${tenantCount} مستأجر بنجاح`, 'success');
        }

        // Display Properties
        function displayProperties() {
            const container = document.getElementById('propertiesList');

            if (properties.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 50px; color: #999;">
                        <i class="fas fa-home" style="font-size: 4rem; margin-bottom: 20px; opacity: 0.3;"></i>
                        <h3>لا توجد عقارات مسجلة</h3>
                        <p>قم بإضافة عقار جديد لبدء الاستخدام</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = properties.map(property => `
                <div class="property-card">
                    <div class="property-header">
                        <div class="property-title">
                            <i class="fas fa-building"></i> ${property.name}
                        </div>
                        <div class="property-actions">
                            <button class="btn btn-success" onclick="addNewTenant('${property.id}')">
                                <i class="fas fa-user-plus"></i> إضافة مستأجر
                            </button>
                            <button class="btn btn-primary" onclick="editProperty('${property.id}')">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-danger" onclick="deleteProperty('${property.id}')">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px; color: #666;">
                        <strong>عدد المستأجرين:</strong> ${property.tenantCount} |
                        <strong>إجمالي الدخل:</strong> ${property.totalIncome.toLocaleString()} ج.م |
                        <strong>تاريخ الإضافة:</strong> ${new Date(property.createdAt).toLocaleDateString('ar-EG')}
                    </div>

                    <div class="tenants-grid">
                        ${property.tenants.map(tenant => `
                            <div class="tenant-card">
                                <div class="tenant-name">
                                    <i class="fas fa-user"></i> ${tenant.name}
                                </div>
                                <div class="tenant-info">
                                    <div><strong>الإيجار:</strong> ${tenant.rentAmount.toLocaleString()} ج.م</div>
                                    <div><strong>الحالة:</strong> <span class="status-${tenant.paymentStatus}">${tenant.paymentStatus === 'paid' ? 'مدفوع' : tenant.paymentStatus === 'partial' ? 'مدفوع جزئياً' : 'غير مدفوع'}</span></div>
                                    <div><strong>التاريخ:</strong> ${tenant.paymentDate ? new Date(tenant.paymentDate).toLocaleDateString('ar-EG') : 'غير محدد'}</div>
                                    <div><strong>التليفون:</strong> ${tenant.phone || 'غير محدد'}</div>
                                    <div><strong>عدد المدفوعات:</strong> ${tenant.paymentHistory ? tenant.paymentHistory.length : 0}</div>
                                    <div><strong>عدد المصروفات:</strong> ${tenant.expenseHistory ? tenant.expenseHistory.length : 0}</div>
                                </div>
                                ${tenant.notes ? `<div style="margin-top: 10px; font-size: 0.85rem; color: #666; font-style: italic;"><strong>ملاحظات:</strong> ${tenant.notes}</div>` : ''}
                                <div class="tenant-actions">
                                    <button class="btn btn-success" onclick="addPayment('${property.id}', '${tenant.id}')" style="font-size: 0.8rem; padding: 6px 10px;">
                                        <i class="fas fa-plus"></i> إضافة دفعة
                                    </button>
                                    <button class="btn btn-info" onclick="viewPaymentHistory('${property.id}', '${tenant.id}')" style="font-size: 0.8rem; padding: 6px 10px;">
                                        <i class="fas fa-history"></i> سجل المدفوعات
                                    </button>
                                    <button class="btn btn-warning" onclick="addExpense('${property.id}', '${tenant.id}')" style="font-size: 0.8rem; padding: 6px 10px;">
                                        <i class="fas fa-minus"></i> إضافة مصروف
                                    </button>
                                    <button class="btn btn-secondary" onclick="viewExpenseHistory('${property.id}', '${tenant.id}')" style="font-size: 0.8rem; padding: 6px 10px;">
                                        <i class="fas fa-list"></i> سجل المصروفات
                                    </button>
                                    <button class="btn btn-primary" onclick="editTenant('${property.id}', '${tenant.id}')" style="font-size: 0.8rem; padding: 6px 10px;">
                                        <i class="fas fa-edit"></i> تعديل المستأجر
                                    </button>
                                    <button class="btn btn-danger" onclick="deleteTenant('${property.id}', '${tenant.id}')" style="font-size: 0.8rem; padding: 6px 10px;">
                                        <i class="fas fa-user-times"></i> حذف المستأجر
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');
        }

        // Update Statistics
        function updateStatistics() {
            const totalProperties = properties.length;
            const totalTenants = properties.reduce((sum, property) => sum + property.tenantCount, 0);

            let totalIncome = 0;
            let totalExpenses = 0;
            let paidTenants = 0;

            const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
            const currentYear = new Date().getFullYear().toString();

            properties.forEach(property => {
                property.tenants.forEach(tenant => {
                    if (!tenant.isEmpty) {
                        // حساب الدخل بناءً على سجل المدفوعات للشهر الحالي
                        let income = 0;
                        let hasPaidThisMonth = false;

                        // التحقق من سجل المدفوعات الجديد
                        if (tenant.paymentHistory && tenant.paymentHistory.length > 0) {
                            tenant.paymentHistory.forEach(payment => {
                                const paymentDate = new Date(payment.date);
                                const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                                const paymentYear = paymentDate.getFullYear().toString();

                                // إذا كان تاريخ الدفع في الشهر الحالي
                                if (paymentMonth === currentMonth && paymentYear === currentYear) {
                                    income += payment.amount;
                                    hasPaidThisMonth = true;
                                }
                            });
                        }
                        // التحقق من البيانات القديمة
                        else if (tenant.paymentDate && tenant.paymentStatus && tenant.paymentStatus !== 'unpaid') {
                            const paymentDate = new Date(tenant.paymentDate);
                            const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                            const paymentYear = paymentDate.getFullYear().toString();

                            // إذا كان تاريخ الدفع في الشهر الحالي
                            if (paymentMonth === currentMonth && paymentYear === currentYear) {
                                if (tenant.paymentStatus === 'paid') {
                                    income = tenant.rentAmount;
                                } else if (tenant.paymentStatus === 'partial') {
                                    income = tenant.rentAmount * 0.5;
                                }
                                hasPaidThisMonth = true;
                            }
                        }

                        if (hasPaidThisMonth) {
                            paidTenants++;
                        }

                        // حساب المصروفات بناءً على سجل المصروفات للشهر الحالي
                        let expenses = 0;
                        if (tenant.expenseHistory && tenant.expenseHistory.length > 0) {
                            tenant.expenseHistory.forEach(expense => {
                                const expenseDate = new Date(expense.date);
                                const expenseMonth = String(expenseDate.getMonth() + 1).padStart(2, '0');
                                const expenseYear = expenseDate.getFullYear().toString();

                                if (expenseMonth === currentMonth && expenseYear === currentYear) {
                                    expenses += expense.amount;
                                }
                            });
                        } else {
                            // استخدام المصروفات القديمة إذا لم يوجد سجل جديد
                            expenses = tenant.monthlyExpenses || 0;
                        }

                        totalIncome += income;
                        totalExpenses += expenses;
                    }
                });
            });

            const netIncome = totalIncome - totalExpenses;

            document.getElementById('totalProperties').textContent = totalProperties;
            document.getElementById('totalTenants').textContent = totalTenants;
            document.getElementById('totalIncome').textContent = `${netIncome.toLocaleString()} ج.م`;
            document.getElementById('paidTenants').textContent = paidTenants;

            // Update report selects when statistics change
            populateReportSelects();
        }

        // Edit Property
        function editProperty(propertyId) {
            const property = properties.find(p => p.id === propertyId);
            if (!property) return;

            // Fill form with existing data
            document.getElementById('propertyName').value = property.name;
            document.getElementById('tenantCount').value = property.tenantCount;

            // Generate tenant fields
            generateTenantFields();

            // Fill tenant data
            setTimeout(() => {
                property.tenants.forEach((tenant, index) => {
                    const i = index + 1;
                    document.querySelector(`input[name="tenantName_${i}"]`).value = tenant.name;
                    document.querySelector(`input[name="rentAmount_${i}"]`).value = tenant.rentAmount;
                    document.querySelector(`select[name="paymentStatus_${i}"]`).value = tenant.paymentStatus;
                    document.querySelector(`input[name="paymentDate_${i}"]`).value = tenant.paymentDate;
                    document.querySelector(`input[name="phone_${i}"]`).value = tenant.phone;
                    document.querySelector(`textarea[name="notes_${i}"]`).value = tenant.notes;
                });
            }, 100);

            // Remove old property and set current ID for update
            properties = properties.filter(p => p.id !== propertyId);
            currentPropertyId = propertyId;

            // Scroll to form
            document.querySelector('.add-property-section').scrollIntoView({ behavior: 'smooth' });

            showNotification('تم تحميل بيانات العقار للتعديل', 'success');
        }

        // Delete Property
        function deleteProperty(propertyId) {
            const property = properties.find(p => p.id === propertyId);
            if (!property) return;

            if (confirm(`هل أنت متأكد من حذف العقار "${property.name}" وجميع بيانات المستأجرين؟`)) {
                properties = properties.filter(p => p.id !== propertyId);
                saveData();
                updateStatistics();
                displayProperties();
                populateReportSelects(); // تحديث قوائم التقارير
                showNotification('تم حذف العقار بنجاح', 'success');
            }
        }

        // Payment Management Functions
        function addPayment(propertyId, tenantId) {
            const property = properties.find(p => p.id === propertyId);
            if (!property) {
                showNotification('العقار غير موجود', 'error');
                return;
            }

            const tenant = property.tenants.find(t => t.id === tenantId);
            if (!tenant) {
                showNotification('المستأجر غير موجود', 'error');
                return;
            }

            // إنشاء نموذج إضافة دفعة
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 500px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h3 style="margin-bottom: 20px; color: #333; text-align: center;">
                        <i class="fas fa-plus-circle"></i> إضافة دفعة جديدة
                    </h3>
                    <p style="margin-bottom: 20px; color: #666; text-align: center;">
                        <strong>المستأجر:</strong> ${tenant.name} | <strong>العقار:</strong> ${property.name}
                    </p>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">تاريخ الدفع:</label>
                        <input type="date" id="paymentDate" style="width: 100%; padding: 10px; border: 2px solid #e0e6ff; border-radius: 8px;" value="${new Date().toISOString().split('T')[0]}">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">مبلغ الدفع (ج.م):</label>
                        <input type="number" id="paymentAmount" style="width: 100%; padding: 10px; border: 2px solid #e0e6ff; border-radius: 8px;" value="${tenant.rentAmount}" min="0" step="0.01">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">حالة الدفع:</label>
                        <select id="paymentStatus" style="width: 100%; padding: 10px; border: 2px solid #e0e6ff; border-radius: 8px;">
                            <option value="paid">مدفوع كاملاً</option>
                            <option value="partial">مدفوع جزئياً</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
                        <textarea id="paymentNotes" style="width: 100%; padding: 10px; border: 2px solid #e0e6ff; border-radius: 8px; height: 80px;" placeholder="ملاحظات إضافية..."></textarea>
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button onclick="savePayment('${propertyId}', '${tenantId}')" style="background: #28a745; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: bold;">
                            <i class="fas fa-save"></i> حفظ الدفعة
                        </button>
                        <button onclick="closePaymentModal()" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: bold;">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            modal.id = 'paymentModal';
        }

        function savePayment(propertyId, tenantId) {
            const paymentDate = document.getElementById('paymentDate').value;
            const paymentAmount = parseFloat(document.getElementById('paymentAmount').value);
            const paymentStatus = document.getElementById('paymentStatus').value;
            const paymentNotes = document.getElementById('paymentNotes').value;

            if (!paymentDate || !paymentAmount || paymentAmount <= 0) {
                showNotification('يرجى ملء جميع البيانات المطلوبة', 'error');
                return;
            }

            const property = properties.find(p => p.id === propertyId);
            const tenant = property.tenants.find(t => t.id === tenantId);

            // إضافة الدفعة إلى سجل المدفوعات
            if (!tenant.paymentHistory) {
                tenant.paymentHistory = [];
            }

            tenant.paymentHistory.push({
                id: generateId(),
                date: paymentDate,
                amount: paymentAmount,
                status: paymentStatus,
                notes: paymentNotes,
                createdAt: new Date().toISOString()
            });

            // تحديث آخر دفعة في بيانات المستأجر الأساسية
            tenant.paymentDate = paymentDate;
            tenant.paymentStatus = paymentStatus;

            // حفظ البيانات
            saveData();
            updateStatistics();
            displayProperties();
            populateReportSelects();

            closePaymentModal();
            showNotification(`تم إضافة دفعة بمبلغ ${paymentAmount.toLocaleString()} ج.م للمستأجر ${tenant.name}`, 'success');
        }

        function closePaymentModal() {
            const modal = document.getElementById('paymentModal');
            if (modal) {
                modal.remove();
            }
        }

        function viewPaymentHistory(propertyId, tenantId) {
            const property = properties.find(p => p.id === propertyId);
            if (!property) return;

            const tenant = property.tenants.find(t => t.id === tenantId);
            if (!tenant) return;

            const payments = tenant.paymentHistory || [];

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 700px; max-height: 80%; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h3 style="margin-bottom: 20px; color: #333; text-align: center;">
                        <i class="fas fa-history"></i> سجل مدفوعات ${tenant.name}
                    </h3>
                    <p style="margin-bottom: 20px; color: #666; text-align: center;">
                        <strong>العقار:</strong> ${property.name} | <strong>إجمالي المدفوعات:</strong> ${payments.length}
                    </p>

                    ${payments.length === 0 ?
                        '<p style="text-align: center; color: #999; padding: 40px;">لا توجد مدفوعات مسجلة</p>' :
                        `<div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr style="background: #667eea; color: white;">
                                        <th style="padding: 12px; text-align: center; border: 1px solid #ddd;">التاريخ</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #ddd;">المبلغ</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #ddd;">الحالة</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #ddd;">ملاحظات</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #ddd;">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${payments.map((payment, index) => `
                                        <tr style="background: ${index % 2 === 0 ? '#f8f9ff' : 'white'};">
                                            <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">${new Date(payment.date).toLocaleDateString('ar-EG')}</td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #ddd; font-weight: bold; color: #28a745;">${payment.amount.toLocaleString()} ج.م</td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">
                                                <span style="padding: 4px 8px; border-radius: 4px; font-size: 0.8rem; background: ${payment.status === 'paid' ? '#d4edda' : '#fff3cd'}; color: ${payment.status === 'paid' ? '#155724' : '#856404'};">
                                                    ${payment.status === 'paid' ? 'مدفوع' : payment.status === 'partial' ? 'مدفوع جزئياً' : 'غير مدفوع'}
                                                </span>
                                            </td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">${payment.notes || '-'}</td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">
                                                <button onclick="deletePayment('${propertyId}', '${tenantId}', '${payment.id}')" style="background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>`
                    }

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closePaymentHistoryModal()" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: bold;">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            modal.id = 'paymentHistoryModal';
        }

        function closePaymentHistoryModal() {
            const modal = document.getElementById('paymentHistoryModal');
            if (modal) {
                modal.remove();
            }
        }

        function deletePayment(propertyId, tenantId, paymentId) {
            if (!confirm('هل أنت متأكد من حذف هذه الدفعة؟')) {
                return;
            }

            const property = properties.find(p => p.id === propertyId);
            const tenant = property.tenants.find(t => t.id === tenantId);

            tenant.paymentHistory = tenant.paymentHistory.filter(p => p.id !== paymentId);

            saveData();
            updateStatistics();
            displayProperties();

            // إعادة فتح نافذة السجل
            closePaymentHistoryModal();
            setTimeout(() => viewPaymentHistory(propertyId, tenantId), 100);

            showNotification('تم حذف الدفعة بنجاح', 'success');
        }

        // Expense Management Functions
        function addExpense(propertyId, tenantId) {
            const property = properties.find(p => p.id === propertyId);
            if (!property) return;

            const tenant = property.tenants.find(t => t.id === tenantId);
            if (!tenant) return;

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 500px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h3 style="margin-bottom: 20px; color: #333; text-align: center;">
                        <i class="fas fa-minus-circle"></i> إضافة مصروف جديد
                    </h3>
                    <p style="margin-bottom: 20px; color: #666; text-align: center;">
                        <strong>العقار:</strong> ${property.name} | <strong>المستأجر:</strong> ${tenant.name}
                    </p>

                    <form id="expenseForm" style="display: flex; flex-direction: column; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">مبلغ المصروف (ج.م):</label>
                            <input type="number" id="expenseAmount" min="0" step="0.01" required
                                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">تاريخ المصروف:</label>
                            <input type="date" id="expenseDate" required
                                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">نوع المصروف:</label>
                            <select id="expenseType" required
                                    style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px;">
                                <option value="">اختر نوع المصروف</option>
                                <option value="maintenance">صيانة</option>
                                <option value="utilities">فواتير</option>
                                <option value="cleaning">تنظيف</option>
                                <option value="repairs">إصلاحات</option>
                                <option value="insurance">تأمين</option>
                                <option value="taxes">ضرائب</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">ملاحظات (اختياري):</label>
                            <textarea id="expenseNotes" rows="3"
                                      style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; resize: vertical;"
                                      placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                            <button type="submit" style="background: #dc3545; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: bold;">
                                <i class="fas fa-save"></i> حفظ المصروف
                            </button>
                            <button type="button" onclick="closeExpenseModal()" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: bold;">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);
            modal.id = 'expenseModal';

            // Set default date to today
            document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];

            // Handle form submission
            document.getElementById('expenseForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const expenseAmount = parseFloat(document.getElementById('expenseAmount').value);
                const expenseDate = document.getElementById('expenseDate').value;
                const expenseType = document.getElementById('expenseType').value;
                const expenseNotes = document.getElementById('expenseNotes').value;

                if (!expenseAmount || !expenseDate || !expenseType) {
                    showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                    return;
                }

                // Initialize expense history if not exists
                if (!tenant.expenseHistory) {
                    tenant.expenseHistory = [];
                }

                // Add new expense
                const newExpense = {
                    id: generateId(),
                    amount: expenseAmount,
                    date: expenseDate,
                    type: expenseType,
                    notes: expenseNotes,
                    createdAt: new Date().toISOString()
                };

                tenant.expenseHistory.push(newExpense);

                saveData();
                updateStatistics();
                displayProperties();

                closeExpenseModal();
                showNotification(`تم إضافة مصروف بمبلغ ${expenseAmount.toLocaleString()} ج.م للمستأجر ${tenant.name}`, 'success');
            });
        }

        function closeExpenseModal() {
            const modal = document.getElementById('expenseModal');
            if (modal) {
                modal.remove();
            }
        }

        function viewExpenseHistory(propertyId, tenantId) {
            const property = properties.find(p => p.id === propertyId);
            if (!property) return;

            const tenant = property.tenants.find(t => t.id === tenantId);
            if (!tenant) return;

            const expenses = tenant.expenseHistory || [];

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            const expenseTypeNames = {
                'maintenance': 'صيانة',
                'utilities': 'فواتير',
                'cleaning': 'تنظيف',
                'repairs': 'إصلاحات',
                'insurance': 'تأمين',
                'taxes': 'ضرائب',
                'other': 'أخرى'
            };

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 700px; max-height: 80%; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h3 style="margin-bottom: 20px; color: #333; text-align: center;">
                        <i class="fas fa-list"></i> سجل مصروفات ${tenant.name}
                    </h3>
                    <p style="margin-bottom: 20px; color: #666; text-align: center;">
                        <strong>العقار:</strong> ${property.name} | <strong>إجمالي المصروفات:</strong> ${expenses.length}
                    </p>

                    ${expenses.length === 0 ?
                        '<p style="text-align: center; color: #999; padding: 40px;">لا توجد مصروفات مسجلة</p>' :
                        `<div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr style="background: #dc3545; color: white;">
                                        <th style="padding: 12px; text-align: center; border: 1px solid #ddd;">التاريخ</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #ddd;">المبلغ</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #ddd;">النوع</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #ddd;">ملاحظات</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #ddd;">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${expenses.map((expense, index) => `
                                        <tr style="background: ${index % 2 === 0 ? '#f8f9ff' : 'white'};">
                                            <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">${new Date(expense.date).toLocaleDateString('ar-EG')}</td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #ddd; font-weight: bold; color: #dc3545;">${expense.amount.toLocaleString()} ج.م</td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">
                                                <span style="padding: 4px 8px; border-radius: 4px; font-size: 0.8rem; background: #fff3cd; color: #856404;">
                                                    ${expenseTypeNames[expense.type] || expense.type}
                                                </span>
                                            </td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">${expense.notes || '-'}</td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">
                                                <button onclick="deleteExpense('${propertyId}', '${tenantId}', '${expense.id}')" style="background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>`
                    }

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeExpenseHistoryModal()" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: bold;">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            modal.id = 'expenseHistoryModal';
        }

        function closeExpenseHistoryModal() {
            const modal = document.getElementById('expenseHistoryModal');
            if (modal) {
                modal.remove();
            }
        }

        function deleteExpense(propertyId, tenantId, expenseId) {
            if (!confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
                return;
            }

            const property = properties.find(p => p.id === propertyId);
            const tenant = property.tenants.find(t => t.id === tenantId);

            tenant.expenseHistory = tenant.expenseHistory.filter(e => e.id !== expenseId);

            saveData();
            updateStatistics();
            displayProperties();

            // إعادة فتح نافذة السجل
            closeExpenseHistoryModal();
            setTimeout(() => viewExpenseHistory(propertyId, tenantId), 100);

            showNotification('تم حذف المصروف بنجاح', 'success');
        }

        // Tenant Management Functions
        function addNewTenant(propertyId) {
            const property = properties.find(p => p.id === propertyId);
            if (!property) {
                showNotification('العقار غير موجود', 'error');
                return;
            }

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 500px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h3 style="margin-bottom: 20px; color: #333; text-align: center;">
                        <i class="fas fa-user-plus"></i> إضافة مستأجر جديد
                    </h3>
                    <p style="margin-bottom: 20px; color: #666; text-align: center;">
                        <strong>العقار:</strong> ${property.name}
                    </p>

                    <form id="newTenantForm" style="display: flex; flex-direction: column; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم المستأجر:</label>
                            <input type="text" id="newTenantName" required
                                   style="width: 100%; padding: 12px; border: 2px solid #e0e6ff; border-radius: 8px; font-size: 16px;"
                                   placeholder="أدخل اسم المستأجر">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">مبلغ الإيجار (ج.م):</label>
                            <input type="number" id="newTenantRent" min="0" step="0.01"
                                   style="width: 100%; padding: 12px; border: 2px solid #e0e6ff; border-radius: 8px; font-size: 16px;"
                                   placeholder="أدخل مبلغ الإيجار">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم التليفون:</label>
                            <input type="tel" id="newTenantPhone"
                                   style="width: 100%; padding: 12px; border: 2px solid #e0e6ff; border-radius: 8px; font-size: 16px;"
                                   placeholder="05xxxxxxxx">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
                            <textarea id="newTenantNotes" rows="3"
                                      style="width: 100%; padding: 12px; border: 2px solid #e0e6ff; border-radius: 8px; font-size: 16px; resize: vertical;"
                                      placeholder="ملاحظات إضافية..."></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                            <button type="submit" style="background: #28a745; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: bold;">
                                <i class="fas fa-save"></i> إضافة المستأجر
                            </button>
                            <button type="button" onclick="closeNewTenantModal()" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: bold;">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);
            modal.id = 'newTenantModal';

            // Handle form submission
            document.getElementById('newTenantForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const tenantName = document.getElementById('newTenantName').value.trim();
                const tenantRent = parseFloat(document.getElementById('newTenantRent').value) || 0;
                const tenantPhone = document.getElementById('newTenantPhone').value.trim();
                const tenantNotes = document.getElementById('newTenantNotes').value.trim();

                if (!tenantName) {
                    showNotification('يرجى إدخال اسم المستأجر', 'error');
                    return;
                }

                // Create new tenant
                const newTenant = {
                    id: generateId(),
                    name: tenantName,
                    rentAmount: tenantRent,
                    paymentStatus: 'unpaid',
                    paymentDate: '',
                    phone: tenantPhone,
                    notes: tenantNotes,
                    paymentHistory: [],
                    expenseHistory: [],
                    isEmpty: false,
                    createdAt: new Date().toISOString()
                };

                // Add to property
                property.tenants.push(newTenant);
                property.tenantCount = property.tenants.length;
                property.updatedAt = new Date().toISOString();

                saveData();
                updateStatistics();
                displayProperties();
                populateReportSelects();

                closeNewTenantModal();
                showNotification(`تم إضافة المستأجر "${tenantName}" بنجاح`, 'success');
            });
        }

        function closeNewTenantModal() {
            const modal = document.getElementById('newTenantModal');
            if (modal) {
                modal.remove();
            }
        }

        function editTenant(propertyId, tenantId) {
            const property = properties.find(p => p.id === propertyId);
            if (!property) return;

            const tenant = property.tenants.find(t => t.id === tenantId);
            if (!tenant) return;

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 500px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h3 style="margin-bottom: 20px; color: #333; text-align: center;">
                        <i class="fas fa-edit"></i> تعديل بيانات المستأجر
                    </h3>
                    <p style="margin-bottom: 20px; color: #666; text-align: center;">
                        <strong>العقار:</strong> ${property.name}
                    </p>

                    <form id="editTenantForm" style="display: flex; flex-direction: column; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم المستأجر:</label>
                            <input type="text" id="editTenantName" required
                                   style="width: 100%; padding: 12px; border: 2px solid #e0e6ff; border-radius: 8px; font-size: 16px;"
                                   value="${tenant.name}">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">مبلغ الإيجار (ج.م):</label>
                            <input type="number" id="editTenantRent" min="0" step="0.01"
                                   style="width: 100%; padding: 12px; border: 2px solid #e0e6ff; border-radius: 8px; font-size: 16px;"
                                   value="${tenant.rentAmount}">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم التليفون:</label>
                            <input type="tel" id="editTenantPhone"
                                   style="width: 100%; padding: 12px; border: 2px solid #e0e6ff; border-radius: 8px; font-size: 16px;"
                                   value="${tenant.phone}">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
                            <textarea id="editTenantNotes" rows="3"
                                      style="width: 100%; padding: 12px; border: 2px solid #e0e6ff; border-radius: 8px; font-size: 16px; resize: vertical;">${tenant.notes}</textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                            <button type="submit" style="background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: bold;">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <button type="button" onclick="closeEditTenantModal()" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: bold;">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);
            modal.id = 'editTenantModal';

            // Handle form submission
            document.getElementById('editTenantForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const tenantName = document.getElementById('editTenantName').value.trim();
                const tenantRent = parseFloat(document.getElementById('editTenantRent').value) || 0;
                const tenantPhone = document.getElementById('editTenantPhone').value.trim();
                const tenantNotes = document.getElementById('editTenantNotes').value.trim();

                if (!tenantName) {
                    showNotification('يرجى إدخال اسم المستأجر', 'error');
                    return;
                }

                // Update tenant data
                tenant.name = tenantName;
                tenant.rentAmount = tenantRent;
                tenant.phone = tenantPhone;
                tenant.notes = tenantNotes;
                tenant.isEmpty = false;

                property.updatedAt = new Date().toISOString();

                saveData();
                updateStatistics();
                displayProperties();
                populateReportSelects();

                closeEditTenantModal();
                showNotification(`تم تحديث بيانات المستأجر "${tenantName}" بنجاح`, 'success');
            });
        }

        function closeEditTenantModal() {
            const modal = document.getElementById('editTenantModal');
            if (modal) {
                modal.remove();
            }
        }

        function deleteTenant(propertyId, tenantId) {
            const property = properties.find(p => p.id === propertyId);
            if (!property) return;

            const tenant = property.tenants.find(t => t.id === tenantId);
            if (!tenant) return;

            if (confirm(`هل أنت متأكد من حذف المستأجر "${tenant.name}" وجميع بياناته؟\n\nسيتم حذف:\n- جميع المدفوعات (${tenant.paymentHistory ? tenant.paymentHistory.length : 0})\n- جميع المصروفات (${tenant.expenseHistory ? tenant.expenseHistory.length : 0})`)) {

                // Remove tenant from property
                property.tenants = property.tenants.filter(t => t.id !== tenantId);
                property.tenantCount = property.tenants.length;
                property.updatedAt = new Date().toISOString();

                saveData();
                updateStatistics();
                displayProperties();
                populateReportSelects();

                showNotification(`تم حذف المستأجر "${tenant.name}" بنجاح`, 'success');
            }
        }

        // Utility Functions
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        // Data Management Functions
        function saveData() {
            const data = {
                properties: properties,
                timestamp: new Date().toISOString(),
                version: '1.0'
            };
            localStorage.setItem('realEstateData', JSON.stringify(data));
        }

        function loadData() {
            try {
                const savedData = localStorage.getItem('realEstateData');
                if (savedData) {
                    const data = JSON.parse(savedData);
                    properties = data.properties || [];

                    // تحديث البيانات القديمة لتشمل سجل المدفوعات
                    let dataUpdated = false;
                    properties.forEach(property => {
                        property.tenants.forEach(tenant => {
                            if (!tenant.paymentHistory) {
                                tenant.paymentHistory = [];
                                dataUpdated = true;

                                // إضافة الدفعة القديمة إلى السجل إذا كانت موجودة
                                if (tenant.paymentDate && tenant.paymentStatus && tenant.paymentStatus !== 'unpaid') {
                                    tenant.paymentHistory.push({
                                        id: generateId(),
                                        date: tenant.paymentDate,
                                        amount: tenant.paymentStatus === 'paid' ? tenant.rentAmount : tenant.rentAmount * 0.5,
                                        status: tenant.paymentStatus,
                                        notes: 'دفعة محولة من النظام القديم',
                                        createdAt: new Date().toISOString()
                                    });
                                    console.log(`تم تحويل دفعة قديمة للمستأجر: ${tenant.name}`);
                                }
                            }

                            // إضافة سجل المصروفات إذا لم يكن موجوداً
                            if (!tenant.expenseHistory) {
                                tenant.expenseHistory = [];
                                dataUpdated = true;
                                console.log(`تم إضافة سجل مصروفات للمستأجر: ${tenant.name}`);
                            }
                        });
                    });

                    // حفظ البيانات المحدثة إذا تم التحديث
                    if (dataUpdated) {
                        console.log('تم تحديث البيانات القديمة لتشمل سجل المدفوعات');
                        saveData();
                    }
                }
            } catch (error) {
                console.error('Error loading data:', error);
                properties = [];
            }

            // تحديث قوائم التقارير بعد تحميل البيانات
            setTimeout(() => {
                populateReportSelects();
            }, 100);
        }

        function exportData() {
            const data = {
                properties: properties,
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(data, null, 2);

            // للهاتف - استخدام مشاركة البيانات
            if (typeof Android !== 'undefined' && Android.shareData) {
                Android.shareData(dataStr);
                showNotification('تم تصدير البيانات بنجاح', 'success');
                return;
            }

            // للمتصفح - تحميل مباشر
            try {
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `real-estate-data-${new Date().toISOString().split('T')[0]}.json`;

                // إضافة الرابط للصفحة وتفعيله ثم حذفه
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // تنظيف الذاكرة
                URL.revokeObjectURL(link.href);

                showNotification('تم تصدير البيانات بنجاح', 'success');
            } catch (error) {
                console.error('خطأ في التصدير:', error);
                showNotification('حدث خطأ أثناء التصدير', 'error');
            }
        }

        function importData(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    if (data.properties && Array.isArray(data.properties)) {
                        if (confirm('هل تريد استبدال البيانات الحالية أم إضافة البيانات الجديدة؟\n\nاضغط "موافق" للاستبدال أو "إلغاء" للإضافة')) {
                            properties = data.properties;
                        } else {
                            properties = [...properties, ...data.properties];
                        }

                        saveData();
                        updateStatistics();
                        displayProperties();
                        populateReportSelects(); // تحديث قوائم التقارير
                        showNotification('تم استيراد البيانات بنجاح', 'success');
                    } else {
                        showNotification('ملف البيانات غير صحيح', 'error');
                    }
                } catch (error) {
                    showNotification('خطأ في قراءة الملف', 'error');
                }
            };
            reader.readAsText(file);

            // Reset file input
            event.target.value = '';
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                properties = [];
                localStorage.removeItem('realEstateData');
                updateStatistics();
                displayProperties();
                populateReportSelects(); // تحديث قوائم التقارير
                showNotification('تم مسح جميع البيانات', 'success');
            }
        }

        // Notification System
        function showNotification(message, type = 'success') {
            // إذا كان التطبيق يعمل في Android WebView
            if (typeof Android !== 'undefined' && Android.showToast) {
                Android.showToast(message);
                return;
            }

            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Android Integration Functions
        function shareDataWithAndroid(data) {
            if (typeof Android !== 'undefined' && Android.shareData) {
                Android.shareData(data);
            } else {
                // Fallback for web browsers
                if (navigator.share) {
                    navigator.share({
                        title: 'تقرير نظام إدارة العقارات',
                        text: data
                    });
                } else {
                    // Copy to clipboard
                    navigator.clipboard.writeText(data).then(() => {
                        showNotification('تم نسخ البيانات إلى الحافظة', 'success');
                    });
                }
            }
        }

        function getDeviceInfo() {
            if (typeof Android !== 'undefined' && Android.getDeviceInfo) {
                return Android.getDeviceInfo();
            }
            return navigator.userAgent;
        }

        // Enhanced Export Functions for Android
        function exportDataForAndroid() {
            const data = {
                properties: properties,
                exportDate: new Date().toISOString(),
                version: '1.0',
                deviceInfo: getDeviceInfo()
            };

            const dataStr = JSON.stringify(data, null, 2);
            shareDataWithAndroid(dataStr);
        }

        // Reports Functions
        function populateReportSelects() {
            // Populate years - نطاق واسع من 2020 إلى 2035
            const currentYear = new Date().getFullYear();
            const years = [];
            for (let year = 2020; year <= 2035; year++) {
                years.push(year);
            }

            const yearSelects = ['reportYear', 'tenantReportYear'];
            yearSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    select.innerHTML = '<option value="">اختر السنة</option>';
                    years.forEach(year => {
                        select.innerHTML += `<option value="${year}">${year}</option>`;
                    });
                }
            });

            // Populate tenants
            const tenantSelect = document.getElementById('tenantSelect');
            if (tenantSelect) {
                tenantSelect.innerHTML = '<option value="">اختر المستأجر</option>';
                properties.forEach(property => {
                    property.tenants.forEach(tenant => {
                        if (!tenant.isEmpty && tenant.name && tenant.name !== `مستأجر ${property.tenants.indexOf(tenant) + 1}`) {
                            tenantSelect.innerHTML += `<option value="${tenant.id}">${tenant.name} - ${property.name}</option>`;
                        }
                    });
                });
            }

            // Populate properties
            const propertySelect = document.getElementById('propertySelect');
            if (propertySelect) {
                propertySelect.innerHTML = '<option value="">اختر العقار</option>';
                properties.forEach(property => {
                    propertySelect.innerHTML += `<option value="${property.id}">${property.name}</option>`;
                });
            }

            console.log('تم تحديث قوائم التقارير:', {
                properties: properties.length,
                tenants: properties.reduce((sum, p) => sum + p.tenants.filter(t => !t.isEmpty).length, 0)
            });

            // إظهار رسالة للمستخدم
            if (properties.length === 0) {
                showNotification('لا توجد عقارات مسجلة. يرجى إضافة عقار أولاً', 'info');
            } else {
                const totalTenants = properties.reduce((sum, p) => sum + p.tenants.filter(t => !t.isEmpty).length, 0);
                if (totalTenants === 0) {
                    showNotification('لا توجد مستأجرين مسجلين. يرجى إضافة مستأجرين أولاً', 'info');
                } else {
                    showNotification(`تم تحديث القوائم: ${properties.length} عقار، ${totalTenants} مستأجر`, 'success');
                }
            }
        }

        function generateMonthlyReport() {
            const month = document.getElementById('reportMonth').value;
            const year = document.getElementById('reportYear').value;

            if (!month || !year) {
                showNotification('يرجى اختيار الشهر والسنة', 'error');
                return;
            }

            const monthNames = {
                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            };

            let totalIncome = 0;
            let totalExpenses = 0;
            let reportData = [];

            properties.forEach(property => {
                property.tenants.forEach(tenant => {
                    if (!tenant.isEmpty) {
                        // البحث في سجل المدفوعات عن دفعات في الشهر المحدد
                        let income = 0;
                        let actualStatus = 'غير مدفوع';
                        let paymentDates = [];

                        // التحقق من سجل المدفوعات الجديد
                        if (tenant.paymentHistory && tenant.paymentHistory.length > 0) {
                            tenant.paymentHistory.forEach(payment => {
                                const paymentDate = new Date(payment.date);
                                const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                                const paymentYear = paymentDate.getFullYear().toString();

                                // إذا كان تاريخ الدفع يطابق الشهر والسنة المطلوبة
                                if (paymentMonth === month && paymentYear === year) {
                                    income += payment.amount;
                                    paymentDates.push(payment.date);

                                    if (payment.status === 'paid') {
                                        actualStatus = 'مدفوع';
                                    } else if (payment.status === 'partial' && actualStatus === 'غير مدفوع') {
                                        actualStatus = 'مدفوع جزئياً';
                                    }
                                }
                            });
                        }
                        // التحقق من البيانات القديمة إذا لم توجد مدفوعات في السجل الجديد
                        else if (tenant.paymentDate && tenant.paymentStatus && tenant.paymentStatus !== 'unpaid') {
                            const paymentDate = new Date(tenant.paymentDate);
                            const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                            const paymentYear = paymentDate.getFullYear().toString();

                            // إذا كان تاريخ الدفع يطابق الشهر والسنة المطلوبة
                            if (paymentMonth === month && paymentYear === year) {
                                if (tenant.paymentStatus === 'paid') {
                                    income = tenant.rentAmount;
                                    actualStatus = 'مدفوع';
                                } else if (tenant.paymentStatus === 'partial') {
                                    income = tenant.rentAmount * 0.5;
                                    actualStatus = 'مدفوع جزئياً';
                                }
                                paymentDates.push(tenant.paymentDate);
                            }
                        }

                        // حساب المصروفات بناءً على سجل المصروفات للشهر المحدد
                        let expenses = 0;
                        if (tenant.expenseHistory && tenant.expenseHistory.length > 0) {
                            tenant.expenseHistory.forEach(expense => {
                                const expenseDate = new Date(expense.date);
                                const expenseMonth = String(expenseDate.getMonth() + 1).padStart(2, '0');
                                const expenseYear = expenseDate.getFullYear().toString();

                                // إذا كان تاريخ المصروف يطابق الشهر والسنة المطلوبة
                                if (expenseMonth === month && expenseYear === year) {
                                    expenses += expense.amount;
                                }
                            });
                        } else {
                            // استخدام المصروفات القديمة إذا لم يوجد سجل جديد
                            expenses = tenant.monthlyExpenses || 0;
                        }

                        totalIncome += income;
                        totalExpenses += expenses;

                        reportData.push({
                            property: property.name,
                            tenant: tenant.name,
                            income: income,
                            expenses: expenses,
                            net: income - expenses,
                            status: actualStatus,
                            paymentDate: paymentDates.length > 0 ? paymentDates.map(d => new Date(d).toLocaleDateString('ar-EG')).join(', ') : 'غير محدد',
                            paymentCount: paymentDates.length
                        });
                    }
                });
            });

            const reportContent = `
                <div class="report-summary">
                    <div class="summary-item income-item">
                        <span>إجمالي الإيرادات:</span>
                        <span>${totalIncome.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item expense-item">
                        <span>إجمالي المصروفات:</span>
                        <span>${totalExpenses.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item net-item">
                        <span>صافي الدخل:</span>
                        <span>${(totalIncome - totalExpenses).toLocaleString()} ج.م</span>
                    </div>
                </div>

                <table class="report-table">
                    <thead>
                        <tr>
                            <th>العقار</th>
                            <th>المستأجر</th>
                            <th>الإيرادات</th>
                            <th>المصروفات</th>
                            <th>صافي الدخل</th>
                            <th>حالة الدفع</th>
                            <th>تاريخ الدفع</th>
                            <th>عدد الدفعات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${reportData.map(row => `
                            <tr>
                                <td>${row.property}</td>
                                <td>${row.tenant}</td>
                                <td class="income-item">${row.income.toLocaleString()} ج.م</td>
                                <td class="expense-item">${row.expenses.toLocaleString()} ج.م</td>
                                <td class="net-item">${row.net.toLocaleString()} ج.م</td>
                                <td><span class="status-${row.status === 'مدفوع' ? 'paid' : row.status === 'مدفوع جزئياً' ? 'partial' : 'unpaid'}">${row.status}</span></td>
                                <td>${row.paymentDate}</td>
                                <td style="text-align: center; font-weight: bold; color: #667eea;">${row.paymentCount || 0}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            // إضافة ملاحظة توضيحية
            const noteContent = `
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                    <h4 style="color: #856404; margin-bottom: 10px;">
                        <i class="fas fa-info-circle"></i> ملاحظة مهمة:
                    </h4>
                    <p style="color: #856404; margin: 0;">
                        هذا التقرير يعرض فقط المدفوعات التي تم دفعها في شهر <strong>${monthNames[month]} ${year}</strong> بناءً على تاريخ الدفع الفعلي.
                        إذا لم تظهر مدفوعات لمستأجر معين، فهذا يعني أنه لم يدفع في هذا الشهر تحديداً.
                        <br><br>
                        <strong>إجمالي المدفوعات في هذا الشهر:</strong> ${reportData.reduce((sum, row) => sum + row.paymentCount, 0)} دفعة
                    </p>
                </div>
            `;

            showReport(`تقرير شهر ${monthNames[month]} ${year}`, noteContent + reportContent);
        }

        function generateTenantReport() {
            const tenantId = document.getElementById('tenantSelect').value;
            const year = document.getElementById('tenantReportYear').value;

            if (!tenantId || !year) {
                showNotification('يرجى اختيار المستأجر والسنة', 'error');
                return;
            }

            console.log('البحث عن المستأجر:', tenantId);
            console.log('العقارات المتاحة:', properties.length);

            let tenant = null;
            let property = null;

            properties.forEach(prop => {
                const foundTenant = prop.tenants.find(t => t.id === tenantId && !t.isEmpty);
                if (foundTenant) {
                    tenant = foundTenant;
                    property = prop;
                    console.log('تم العثور على المستأجر:', tenant.name, 'في العقار:', property.name);
                }
            });

            if (!tenant) {
                console.log('لم يتم العثور على المستأجر');
                showNotification('المستأجر غير موجود أو تم حذفه', 'error');
                populateReportSelects(); // إعادة تحديث القوائم
                return;
            }

            const monthlyData = [];
            for (let month = 1; month <= 12; month++) {
                // حساب الدخل بناءً على سجل المدفوعات للشهر المحدد
                let income = 0;
                const monthStr = String(month).padStart(2, '0');

                if (tenant.paymentHistory && tenant.paymentHistory.length > 0) {
                    tenant.paymentHistory.forEach(payment => {
                        const paymentDate = new Date(payment.date);
                        const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                        const paymentYear = paymentDate.getFullYear().toString();

                        if (paymentMonth === monthStr && paymentYear === year) {
                            income += payment.amount;
                        }
                    });
                } else if (tenant.paymentDate && tenant.paymentStatus && tenant.paymentStatus !== 'unpaid') {
                    const paymentDate = new Date(tenant.paymentDate);
                    const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                    const paymentYear = paymentDate.getFullYear().toString();

                    if (paymentMonth === monthStr && paymentYear === year) {
                        if (tenant.paymentStatus === 'paid') {
                            income = tenant.rentAmount;
                        } else if (tenant.paymentStatus === 'partial') {
                            income = tenant.rentAmount * 0.5;
                        }
                    }
                }

                // حساب المصروفات بناءً على سجل المصروفات للشهر المحدد
                let expenses = 0;
                if (tenant.expenseHistory && tenant.expenseHistory.length > 0) {
                    tenant.expenseHistory.forEach(expense => {
                        const expenseDate = new Date(expense.date);
                        const expenseMonth = String(expenseDate.getMonth() + 1).padStart(2, '0');
                        const expenseYear = expenseDate.getFullYear().toString();

                        if (expenseMonth === monthStr && expenseYear === year) {
                            expenses += expense.amount;
                        }
                    });
                } else {
                    // استخدام المصروفات القديمة إذا لم يوجد سجل جديد
                    expenses = tenant.monthlyExpenses || 0;
                }

                monthlyData.push({
                    month: month,
                    income: income,
                    expenses: expenses,
                    net: income - expenses
                });
            }

            const totalYearIncome = monthlyData.reduce((sum, data) => sum + data.income, 0);
            const totalYearExpenses = monthlyData.reduce((sum, data) => sum + data.expenses, 0);

            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            const reportContent = `
                <div style="margin-bottom: 20px;">
                    <p><strong>العقار:</strong> ${property.name}</p>
                    <p><strong>المستأجر:</strong> ${tenant.name}</p>
                    <p><strong>رقم التليفون:</strong> ${tenant.phone || 'غير محدد'}</p>
                    <p><strong>السنة:</strong> ${year}</p>
                </div>

                <div class="report-summary">
                    <div class="summary-item income-item">
                        <span>إجمالي الإيرادات السنوية:</span>
                        <span>${totalYearIncome.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item expense-item">
                        <span>إجمالي المصروفات السنوية:</span>
                        <span>${totalYearExpenses.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item net-item">
                        <span>صافي الدخل السنوي:</span>
                        <span>${(totalYearIncome - totalYearExpenses).toLocaleString()} ج.م</span>
                    </div>
                </div>

                <table class="report-table">
                    <thead>
                        <tr>
                            <th>الشهر</th>
                            <th>الإيرادات</th>
                            <th>المصروفات</th>
                            <th>صافي الدخل</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${monthlyData.map((data, index) => `
                            <tr>
                                <td>${monthNames[index]}</td>
                                <td class="income-item">${data.income.toLocaleString()} ج.م</td>
                                <td class="expense-item">${data.expenses.toLocaleString()} ج.م</td>
                                <td class="net-item">${data.net.toLocaleString()} ج.م</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            showReport(`التقرير السنوي للمستأجر ${tenant.name} - ${year}`, reportContent);
        }

        function generatePropertyReport() {
            const propertyId = document.getElementById('propertySelect').value;

            if (!propertyId) {
                showNotification('يرجى اختيار العقار', 'error');
                return;
            }

            console.log('البحث عن العقار:', propertyId);
            console.log('العقارات المتاحة:', properties.length);

            const property = properties.find(p => p.id === propertyId);
            if (!property) {
                console.log('لم يتم العثور على العقار');
                showNotification('العقار غير موجود أو تم حذفه', 'error');
                populateReportSelects(); // إعادة تحديث القوائم
                return;
            }

            console.log('تم العثور على العقار:', property.name);

            let totalIncome = 0;
            let totalExpenses = 0;
            let paidTenants = 0;
            let unpaidTenants = 0;

            const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
            const currentYear = new Date().getFullYear().toString();

            const tenantData = property.tenants.map(tenant => {
                if (!tenant.isEmpty) {
                    // حساب الدخل بناءً على سجل المدفوعات للشهر الحالي
                    let income = 0;
                    let actualStatus = 'غير مدفوع';

                    if (tenant.paymentHistory && tenant.paymentHistory.length > 0) {
                        tenant.paymentHistory.forEach(payment => {
                            const paymentDate = new Date(payment.date);
                            const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                            const paymentYear = paymentDate.getFullYear().toString();

                            if (paymentMonth === currentMonth && paymentYear === currentYear) {
                                income += payment.amount;
                                if (payment.status === 'paid') {
                                    actualStatus = 'مدفوع';
                                } else if (payment.status === 'partial' && actualStatus === 'غير مدفوع') {
                                    actualStatus = 'مدفوع جزئياً';
                                }
                            }
                        });
                    } else if (tenant.paymentDate && tenant.paymentStatus && tenant.paymentStatus !== 'unpaid') {
                        const paymentDate = new Date(tenant.paymentDate);
                        const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                        const paymentYear = paymentDate.getFullYear().toString();

                        if (paymentMonth === currentMonth && paymentYear === currentYear) {
                            if (tenant.paymentStatus === 'paid') {
                                income = tenant.rentAmount;
                                actualStatus = 'مدفوع';
                            } else if (tenant.paymentStatus === 'partial') {
                                income = tenant.rentAmount * 0.5;
                                actualStatus = 'مدفوع جزئياً';
                            }
                        }
                    }

                    // حساب المصروفات بناءً على سجل المصروفات للشهر الحالي
                    let expenses = 0;
                    if (tenant.expenseHistory && tenant.expenseHistory.length > 0) {
                        tenant.expenseHistory.forEach(expense => {
                            const expenseDate = new Date(expense.date);
                            const expenseMonth = String(expenseDate.getMonth() + 1).padStart(2, '0');
                            const expenseYear = expenseDate.getFullYear().toString();

                            if (expenseMonth === currentMonth && expenseYear === currentYear) {
                                expenses += expense.amount;
                            }
                        });
                    } else {
                        // استخدام المصروفات القديمة إذا لم يوجد سجل جديد
                        expenses = tenant.monthlyExpenses || 0;
                    }

                    totalIncome += income;
                    totalExpenses += expenses;

                    if (actualStatus === 'مدفوع') paidTenants++;
                    else if (actualStatus === 'غير مدفوع') unpaidTenants++;

                    return {
                        name: tenant.name,
                        income: income,
                        expenses: expenses,
                        net: income - expenses,
                        status: actualStatus,
                        phone: tenant.phone
                    };
                }
                return null;
            }).filter(t => t !== null);

            const reportContent = `
                <div style="margin-bottom: 20px;">
                    <p><strong>اسم العقار:</strong> ${property.name}</p>
                    <p><strong>عدد المستأجرين:</strong> ${property.tenantCount}</p>
                    <p><strong>المستأجرين المدفوعين:</strong> ${paidTenants}</p>
                    <p><strong>المستأجرين غير المدفوعين:</strong> ${unpaidTenants}</p>
                </div>

                <div class="report-summary">
                    <div class="summary-item income-item">
                        <span>إجمالي الإيرادات الشهرية:</span>
                        <span>${totalIncome.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item expense-item">
                        <span>إجمالي المصروفات الشهرية:</span>
                        <span>${totalExpenses.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item net-item">
                        <span>صافي الدخل الشهري:</span>
                        <span>${(totalIncome - totalExpenses).toLocaleString()} ج.م</span>
                    </div>
                </div>

                <table class="report-table">
                    <thead>
                        <tr>
                            <th>المستأجر</th>
                            <th>الإيرادات</th>
                            <th>المصروفات</th>
                            <th>صافي الدخل</th>
                            <th>حالة الدفع</th>
                            <th>التليفون</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tenantData.map(tenant => `
                            <tr>
                                <td>${tenant.name}</td>
                                <td class="income-item">${tenant.income.toLocaleString()} ج.م</td>
                                <td class="expense-item">${tenant.expenses.toLocaleString()} ج.م</td>
                                <td class="net-item">${tenant.net.toLocaleString()} ج.م</td>
                                <td><span class="status-${tenant.status === 'مدفوع' ? 'paid' : tenant.status === 'مدفوع جزئياً' ? 'partial' : 'unpaid'}">${tenant.status}</span></td>
                                <td>${tenant.phone || 'غير محدد'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            showReport(`تقرير العقار: ${property.name}`, reportContent);
        }

        function showReport(title, content) {
            document.getElementById('reportTitle').textContent = title;
            document.getElementById('reportContent').innerHTML = content;
            document.getElementById('reportDisplay').style.display = 'block';
            document.getElementById('reportDisplay').scrollIntoView({ behavior: 'smooth' });
        }

        function closeReport() {
            document.getElementById('reportDisplay').style.display = 'none';
        }

        // PDF Export Functions
        function exportMonthlyReportPDF() {
            const month = document.getElementById('reportMonth').value;
            const year = document.getElementById('reportYear').value;

            if (!month || !year) {
                showNotification('يرجى اختيار الشهر والسنة أولاً', 'error');
                return;
            }

            const monthNames = {
                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            };

            let totalIncome = 0;
            let totalExpenses = 0;
            let reportData = [];

            properties.forEach(property => {
                property.tenants.forEach(tenant => {
                    if (!tenant.isEmpty) {
                        // البحث في سجل المدفوعات عن دفعات في الشهر المحدد
                        let income = 0;
                        let actualStatus = 'غير مدفوع';
                        let paymentDates = [];

                        // التحقق من سجل المدفوعات الجديد
                        if (tenant.paymentHistory && tenant.paymentHistory.length > 0) {
                            tenant.paymentHistory.forEach(payment => {
                                const paymentDate = new Date(payment.date);
                                const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                                const paymentYear = paymentDate.getFullYear().toString();

                                // إذا كان تاريخ الدفع يطابق الشهر والسنة المطلوبة
                                if (paymentMonth === month && paymentYear === year) {
                                    income += payment.amount;
                                    paymentDates.push(payment.date);

                                    if (payment.status === 'paid') {
                                        actualStatus = 'مدفوع';
                                    } else if (payment.status === 'partial' && actualStatus === 'غير مدفوع') {
                                        actualStatus = 'مدفوع جزئياً';
                                    }
                                }
                            });
                        }
                        // التحقق من البيانات القديمة إذا لم توجد مدفوعات في السجل الجديد
                        else if (tenant.paymentDate && tenant.paymentStatus && tenant.paymentStatus !== 'unpaid') {
                            const paymentDate = new Date(tenant.paymentDate);
                            const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                            const paymentYear = paymentDate.getFullYear().toString();

                            // إذا كان تاريخ الدفع يطابق الشهر والسنة المطلوبة
                            if (paymentMonth === month && paymentYear === year) {
                                if (tenant.paymentStatus === 'paid') {
                                    income = tenant.rentAmount;
                                    actualStatus = 'مدفوع';
                                } else if (tenant.paymentStatus === 'partial') {
                                    income = tenant.rentAmount * 0.5;
                                    actualStatus = 'مدفوع جزئياً';
                                }
                                paymentDates.push(tenant.paymentDate);
                            }
                        }

                        const expenses = tenant.monthlyExpenses || 0;

                        totalIncome += income;
                        totalExpenses += expenses;

                        reportData.push({
                            property: property.name,
                            tenant: tenant.name,
                            income: income,
                            expenses: expenses,
                            net: income - expenses,
                            status: actualStatus,
                            paymentDate: paymentDates.length > 0 ? paymentDates.map(d => new Date(d).toLocaleDateString('ar-EG')).join(', ') : 'غير محدد',
                            paymentCount: paymentDates.length
                        });
                    }
                });
            });

            // Create PDF with better formatting
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF('p', 'mm', 'a4');

            // Title in Arabic and English
            doc.setFontSize(18);
            doc.setFont('helvetica', 'bold');
            doc.text(`Monthly Report - ${monthNames[month]} ${year}`, 105, 20, { align: 'center' });
            doc.text(`تقرير شهر ${monthNames[month]} ${year}`, 105, 30, { align: 'center' });

            // Add line under title
            doc.setLineWidth(0.5);
            doc.line(20, 35, 190, 35);

            // Summary section with better formatting
            doc.setFontSize(14);
            doc.setFont('helvetica', 'bold');
            doc.text('Monthly Summary / الملخص الشهري', 20, 50);

            doc.setFontSize(11);
            doc.setFont('helvetica', 'normal');
            doc.text(`Total Income / اجمالي الايرادات: ${totalIncome.toLocaleString()} EGP`, 20, 60);
            doc.text(`Total Expenses / اجمالي المصروفات: ${totalExpenses.toLocaleString()} EGP`, 20, 68);
            doc.text(`Net Income / صافي الدخل: ${(totalIncome - totalExpenses).toLocaleString()} EGP`, 20, 76);

            // Table with better structure
            doc.setFontSize(10);
            doc.setFont('helvetica', 'bold');
            let yPos = 90;

            // Table headers with background
            doc.setFillColor(102, 126, 234);
            doc.rect(15, yPos - 5, 180, 8, 'F');
            doc.setTextColor(255, 255, 255);
            doc.text('Property/العقار', 20, yPos);
            doc.text('Tenant/المستاجر', 55, yPos);
            doc.text('Income/الايراد', 90, yPos);
            doc.text('Expenses/المصروفات', 115, yPos);
            doc.text('Net/الصافي', 145, yPos);
            doc.text('Status/الحالة', 165, yPos);

            // Reset text color for data
            doc.setTextColor(0, 0, 0);
            doc.setFont('helvetica', 'normal');
            yPos += 12;

            // Table data with alternating row colors
            reportData.forEach((row, index) => {
                if (yPos > 270) {
                    doc.addPage();
                    yPos = 20;
                }

                // Alternating row background
                if (index % 2 === 0) {
                    doc.setFillColor(248, 249, 255);
                    doc.rect(15, yPos - 3, 180, 6, 'F');
                }

                doc.text(row.property.substring(0, 12), 20, yPos);
                doc.text(row.tenant.substring(0, 12), 55, yPos);
                doc.text(`${row.income.toLocaleString()}`, 90, yPos);
                doc.text(`${row.expenses.toLocaleString()}`, 115, yPos);
                doc.text(`${row.net.toLocaleString()}`, 145, yPos);
                doc.text(row.status.substring(0, 8), 165, yPos);

                yPos += 6;
            });

            // Footer with better formatting
            doc.setFontSize(8);
            doc.setTextColor(128, 128, 128);
            doc.text(`Generated on / تاريخ الانشاء: ${new Date().toLocaleDateString('ar-EG')}`, 20, 285);
            doc.text('Real Estate Management System / نظام ادارة العقارات', 105, 285, { align: 'center' });

            // Save PDF
            doc.save(`تقرير-${monthNames[month]}-${year}.pdf`);
            showNotification(`تم تصدير تقرير ${monthNames[month]} ${year} بصيغة PDF`, 'success');
        }

        function exportTenantReportPDF() {
            const tenantId = document.getElementById('tenantSelect').value;
            const year = document.getElementById('tenantReportYear').value;

            if (!tenantId || !year) {
                showNotification('يرجى اختيار المستأجر والسنة', 'error');
                return;
            }

            let tenant = null;
            let property = null;

            properties.forEach(prop => {
                const foundTenant = prop.tenants.find(t => t.id === tenantId);
                if (foundTenant) {
                    tenant = foundTenant;
                    property = prop;
                }
            });

            if (!tenant) {
                showNotification('المستأجر غير موجود', 'error');
                return;
            }

            const monthlyData = [];
            for (let month = 1; month <= 12; month++) {
                const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                             tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                const expenses = tenant.monthlyExpenses || 0;

                monthlyData.push({
                    month: month,
                    income: income,
                    expenses: expenses,
                    net: income - expenses
                });
            }

            const totalYearIncome = monthlyData.reduce((sum, data) => sum + data.income, 0);
            const totalYearExpenses = monthlyData.reduce((sum, data) => sum + data.expenses, 0);

            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            // Create PDF
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Title
            doc.setFontSize(18);
            doc.text(`Annual Tenant Report ${year}`, 105, 20, { align: 'center' });
            doc.text(`التقرير السنوي للمستأجر ${year}`, 105, 30, { align: 'center' });

            // Tenant Info
            doc.setFontSize(12);
            doc.text(`Property / العقار: ${property.name}`, 20, 50);
            doc.text(`Tenant / المستأجر: ${tenant.name}`, 20, 60);
            doc.text(`Phone / التليفون: ${tenant.phone || 'غير محدد'}`, 20, 70);

            // Summary
            doc.setFontSize(14);
            doc.text('Annual Summary / الملخص السنوي:', 20, 90);
            doc.setFontSize(12);
            doc.text(`Total Income / إجمالي الإيرادات: ${totalYearIncome.toLocaleString()} ج.م`, 20, 100);
            doc.text(`Total Expenses / إجمالي المصروفات: ${totalYearExpenses.toLocaleString()} ج.م`, 20, 110);
            doc.text(`Net Income / صافي الدخل: ${(totalYearIncome - totalYearExpenses).toLocaleString()} ج.م`, 20, 120);

            // Monthly breakdown
            doc.setFontSize(10);
            let yPos = 140;
            doc.text('Month', 20, yPos);
            doc.text('Income', 80, yPos);
            doc.text('Expenses', 120, yPos);
            doc.text('Net', 160, yPos);

            yPos += 10;
            monthlyData.forEach((data, index) => {
                if (yPos > 270) {
                    doc.addPage();
                    yPos = 20;
                }

                doc.text(monthNames[index], 20, yPos);
                doc.text(`${data.income.toLocaleString()} ج.م`, 80, yPos);
                doc.text(`${data.expenses.toLocaleString()} ج.م`, 120, yPos);
                doc.text(`${data.net.toLocaleString()} ج.م`, 160, yPos);

                yPos += 8;
            });

            // Footer
            doc.setFontSize(8);
            doc.text(`Generated on: ${new Date().toLocaleDateString('ar-EG')}`, 20, 290);
            doc.text('Real Estate Management System', 150, 290);

            // Save PDF
            doc.save(`تقرير-${tenant.name}-${year}.pdf`);
            showNotification(`تم تصدير التقرير السنوي للمستأجر ${tenant.name}`, 'success');
        }

        function exportPropertyReportPDF() {
            const propertyId = document.getElementById('propertySelect').value;

            if (!propertyId) {
                showNotification('يرجى اختيار العقار', 'error');
                return;
            }

            const property = properties.find(p => p.id === propertyId);
            if (!property) {
                showNotification('العقار غير موجود', 'error');
                return;
            }

            let totalIncome = 0;
            let totalExpenses = 0;
            let paidTenants = 0;
            let unpaidTenants = 0;

            const tenantData = property.tenants.map(tenant => {
                if (!tenant.isEmpty) {
                    const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                                 tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                    const expenses = tenant.monthlyExpenses || 0;

                    totalIncome += income;
                    totalExpenses += expenses;

                    if (tenant.paymentStatus === 'paid') paidTenants++;
                    else if (tenant.paymentStatus === 'unpaid') unpaidTenants++;

                    return {
                        name: tenant.name,
                        income: income,
                        expenses: expenses,
                        net: income - expenses,
                        status: tenant.paymentStatus === 'paid' ? 'مدفوع' : tenant.paymentStatus === 'partial' ? 'مدفوع جزئياً' : 'غير مدفوع',
                        phone: tenant.phone
                    };
                }
                return null;
            }).filter(t => t !== null);

            // Create PDF
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Title
            doc.setFontSize(18);
            doc.text('Property Report', 105, 20, { align: 'center' });
            doc.text(`تقرير العقار: ${property.name}`, 105, 30, { align: 'center' });

            // Property Info
            doc.setFontSize(12);
            doc.text(`Property Name / اسم العقار: ${property.name}`, 20, 50);
            doc.text(`Total Tenants / عدد المستأجرين: ${property.tenantCount}`, 20, 60);
            doc.text(`Paid Tenants / المدفوعين: ${paidTenants}`, 20, 70);
            doc.text(`Unpaid Tenants / غير المدفوعين: ${unpaidTenants}`, 20, 80);

            // Summary
            doc.setFontSize(14);
            doc.text('Monthly Summary / الملخص الشهري:', 20, 100);
            doc.setFontSize(12);
            doc.text(`Total Income / إجمالي الإيرادات: ${totalIncome.toLocaleString()} ج.م`, 20, 110);
            doc.text(`Total Expenses / إجمالي المصروفات: ${totalExpenses.toLocaleString()} ج.م`, 20, 120);
            doc.text(`Net Income / صافي الدخل: ${(totalIncome - totalExpenses).toLocaleString()} ج.م`, 20, 130);

            // Tenants table
            doc.setFontSize(10);
            let yPos = 150;
            doc.text('Tenant', 20, yPos);
            doc.text('Income', 70, yPos);
            doc.text('Expenses', 100, yPos);
            doc.text('Net', 130, yPos);
            doc.text('Status', 160, yPos);

            yPos += 10;
            tenantData.forEach((tenant, index) => {
                if (yPos > 270) {
                    doc.addPage();
                    yPos = 20;
                }

                doc.text(tenant.name.substring(0, 20), 20, yPos);
                doc.text(`${tenant.income.toLocaleString()}`, 70, yPos);
                doc.text(`${tenant.expenses.toLocaleString()}`, 100, yPos);
                doc.text(`${tenant.net.toLocaleString()}`, 130, yPos);
                doc.text(tenant.status, 160, yPos);

                yPos += 8;
            });

            // Footer
            doc.setFontSize(8);
            doc.text(`Generated on: ${new Date().toLocaleDateString('ar-EG')}`, 20, 290);
            doc.text('Real Estate Management System', 150, 290);

            // Save PDF
            doc.save(`تقرير-عقار-${property.name}.pdf`);
            showNotification(`تم تصدير تقرير العقار ${property.name}`, 'success');
        }

        // Excel Export Functions
        function exportMonthlyReportExcel() {
            const month = document.getElementById('reportMonth').value;
            const year = document.getElementById('reportYear').value;

            if (!month || !year) {
                showNotification('يرجى اختيار الشهر والسنة أولاً', 'error');
                return;
            }

            const monthNames = {
                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            };

            let totalIncome = 0;
            let totalExpenses = 0;
            let reportData = [];

            properties.forEach(property => {
                property.tenants.forEach(tenant => {
                    if (!tenant.isEmpty) {
                        // البحث في سجل المدفوعات عن دفعات في الشهر المحدد
                        let income = 0;
                        let actualStatus = 'غير مدفوع';
                        let paymentDates = [];

                        // التحقق من سجل المدفوعات الجديد
                        if (tenant.paymentHistory && tenant.paymentHistory.length > 0) {
                            tenant.paymentHistory.forEach(payment => {
                                const paymentDate = new Date(payment.date);
                                const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                                const paymentYear = paymentDate.getFullYear().toString();

                                // إذا كان تاريخ الدفع يطابق الشهر والسنة المطلوبة
                                if (paymentMonth === month && paymentYear === year) {
                                    income += payment.amount;
                                    paymentDates.push(payment.date);

                                    if (payment.status === 'paid') {
                                        actualStatus = 'مدفوع';
                                    } else if (payment.status === 'partial' && actualStatus === 'غير مدفوع') {
                                        actualStatus = 'مدفوع جزئياً';
                                    }
                                }
                            });
                        }
                        // التحقق من البيانات القديمة إذا لم توجد مدفوعات في السجل الجديد
                        else if (tenant.paymentDate && tenant.paymentStatus && tenant.paymentStatus !== 'unpaid') {
                            const paymentDate = new Date(tenant.paymentDate);
                            const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                            const paymentYear = paymentDate.getFullYear().toString();

                            // إذا كان تاريخ الدفع يطابق الشهر والسنة المطلوبة
                            if (paymentMonth === month && paymentYear === year) {
                                if (tenant.paymentStatus === 'paid') {
                                    income = tenant.rentAmount;
                                    actualStatus = 'مدفوع';
                                } else if (tenant.paymentStatus === 'partial') {
                                    income = tenant.rentAmount * 0.5;
                                    actualStatus = 'مدفوع جزئياً';
                                }
                                paymentDates.push(tenant.paymentDate);
                            }
                        }

                        const expenses = tenant.monthlyExpenses || 0;

                        totalIncome += income;
                        totalExpenses += expenses;

                        reportData.push({
                            'العقار': property.name,
                            'المستأجر': tenant.name,
                            'الإيرادات (ج.م)': income,
                            'المصروفات (ج.م)': expenses,
                            'صافي الدخل (ج.م)': income - expenses,
                            'حالة الدفع': actualStatus,
                            'تاريخ الدفع': paymentDates.length > 0 ? paymentDates.map(d => new Date(d).toLocaleDateString('ar-EG')).join(', ') : 'غير محدد',
                            'عدد الدفعات': paymentDates.length,
                            'التليفون': tenant.phone || 'غير محدد'
                        });
                    }
                });
            });

            // Add summary row
            reportData.unshift({
                'العقار': 'الملخص',
                'المستأجر': `${monthNames[month]} ${year}`,
                'الإيرادات (ج.م)': totalIncome,
                'المصروفات (ج.م)': totalExpenses,
                'صافي الدخل (ج.م)': totalIncome - totalExpenses,
                'حالة الدفع': 'إجمالي',
                'التليفون': new Date().toLocaleDateString('ar-EG')
            });

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(reportData);

            // Set column widths
            ws['!cols'] = [
                { width: 20 }, // العقار
                { width: 20 }, // المستأجر
                { width: 15 }, // الإيرادات
                { width: 15 }, // المصروفات
                { width: 15 }, // صافي الدخل
                { width: 15 }, // حالة الدفع
                { width: 20 }, // تاريخ الدفع
                { width: 12 }, // عدد الدفعات
                { width: 15 }  // التليفون
            ];

            XLSX.utils.book_append_sheet(wb, ws, 'التقرير الشهري');

            // Save Excel file
            XLSX.writeFile(wb, `تقرير-${monthNames[month]}-${year}.xlsx`);
            showNotification(`تم تصدير تقرير ${monthNames[month]} ${year} بصيغة Excel`, 'success');
        }

        function exportTenantReportExcel() {
            const tenantId = document.getElementById('tenantSelect').value;
            const year = document.getElementById('tenantReportYear').value;

            if (!tenantId || !year) {
                showNotification('يرجى اختيار المستأجر والسنة', 'error');
                return;
            }

            let tenant = null;
            let property = null;

            properties.forEach(prop => {
                const foundTenant = prop.tenants.find(t => t.id === tenantId);
                if (foundTenant) {
                    tenant = foundTenant;
                    property = prop;
                }
            });

            if (!tenant) {
                showNotification('المستأجر غير موجود', 'error');
                return;
            }

            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            const monthlyData = [];
            let totalYearIncome = 0;
            let totalYearExpenses = 0;

            for (let month = 1; month <= 12; month++) {
                // حساب الدخل بناءً على سجل المدفوعات للشهر المحدد
                let income = 0;
                const monthStr = String(month).padStart(2, '0');

                if (tenant.paymentHistory && tenant.paymentHistory.length > 0) {
                    tenant.paymentHistory.forEach(payment => {
                        const paymentDate = new Date(payment.date);
                        const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                        const paymentYear = paymentDate.getFullYear().toString();

                        if (paymentMonth === monthStr && paymentYear === year) {
                            income += payment.amount;
                        }
                    });
                } else if (tenant.paymentDate && tenant.paymentStatus && tenant.paymentStatus !== 'unpaid') {
                    const paymentDate = new Date(tenant.paymentDate);
                    const paymentMonth = String(paymentDate.getMonth() + 1).padStart(2, '0');
                    const paymentYear = paymentDate.getFullYear().toString();

                    if (paymentMonth === monthStr && paymentYear === year) {
                        if (tenant.paymentStatus === 'paid') {
                            income = tenant.rentAmount;
                        } else if (tenant.paymentStatus === 'partial') {
                            income = tenant.rentAmount * 0.5;
                        }
                    }
                }

                // حساب المصروفات بناءً على سجل المصروفات للشهر المحدد
                let expenses = 0;
                if (tenant.expenseHistory && tenant.expenseHistory.length > 0) {
                    tenant.expenseHistory.forEach(expense => {
                        const expenseDate = new Date(expense.date);
                        const expenseMonth = String(expenseDate.getMonth() + 1).padStart(2, '0');
                        const expenseYear = expenseDate.getFullYear().toString();

                        if (expenseMonth === monthStr && expenseYear === year) {
                            expenses += expense.amount;
                        }
                    });
                } else {
                    // استخدام المصروفات القديمة إذا لم يوجد سجل جديد
                    expenses = tenant.monthlyExpenses || 0;
                }

                totalYearIncome += income;
                totalYearExpenses += expenses;

                monthlyData.push({
                    'الشهر': monthNames[month - 1],
                    'الإيرادات (ج.م)': income,
                    'المصروفات (ج.م)': expenses,
                    'صافي الدخل (ج.م)': income - expenses
                });
            }

            // Add summary row
            monthlyData.unshift({
                'الشهر': 'الملخص السنوي',
                'الإيرادات (ج.م)': totalYearIncome,
                'المصروفات (ج.م)': totalYearExpenses,
                'صافي الدخل (ج.م)': totalYearIncome - totalYearExpenses
            });

            // Add tenant info
            monthlyData.unshift({
                'الشهر': 'معلومات المستأجر',
                'الإيرادات (ج.م)': `العقار: ${property.name}`,
                'المصروفات (ج.م)': `المستأجر: ${tenant.name}`,
                'صافي الدخل (ج.م)': `التليفون: ${tenant.phone || 'غير محدد'}`
            });

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(monthlyData);

            // Set column widths
            ws['!cols'] = [
                { width: 20 }, // الشهر
                { width: 20 }, // الإيرادات
                { width: 20 }, // المصروفات
                { width: 20 }  // صافي الدخل
            ];

            XLSX.utils.book_append_sheet(wb, ws, 'التقرير السنوي');

            // Save Excel file
            XLSX.writeFile(wb, `تقرير-${tenant.name}-${year}.xlsx`);
            showNotification(`تم تصدير التقرير السنوي للمستأجر ${tenant.name}`, 'success');
        }

        function exportPropertyReportExcel() {
            const propertyId = document.getElementById('propertySelect').value;

            if (!propertyId) {
                showNotification('يرجى اختيار العقار', 'error');
                return;
            }

            const property = properties.find(p => p.id === propertyId);
            if (!property) {
                showNotification('العقار غير موجود', 'error');
                return;
            }

            let totalIncome = 0;
            let totalExpenses = 0;
            let paidTenants = 0;
            let unpaidTenants = 0;

            const tenantData = [];

            property.tenants.forEach(tenant => {
                if (!tenant.isEmpty) {
                    const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                                 tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                    const expenses = tenant.monthlyExpenses || 0;

                    totalIncome += income;
                    totalExpenses += expenses;

                    if (tenant.paymentStatus === 'paid') paidTenants++;
                    else if (tenant.paymentStatus === 'unpaid') unpaidTenants++;

                    tenantData.push({
                        'المستأجر': tenant.name,
                        'الإيرادات (ج.م)': income,
                        'المصروفات (ج.م)': expenses,
                        'صافي الدخل (ج.م)': income - expenses,
                        'حالة الدفع': tenant.paymentStatus === 'paid' ? 'مدفوع' : tenant.paymentStatus === 'partial' ? 'مدفوع جزئياً' : 'غير مدفوع',
                        'التليفون': tenant.phone || 'غير محدد'
                    });
                }
            });

            // Add summary row
            tenantData.unshift({
                'المستأجر': 'الملخص الإجمالي',
                'الإيرادات (ج.م)': totalIncome,
                'المصروفات (ج.م)': totalExpenses,
                'صافي الدخل (ج.م)': totalIncome - totalExpenses,
                'حالة الدفع': `مدفوع: ${paidTenants} | غير مدفوع: ${unpaidTenants}`,
                'التليفون': new Date().toLocaleDateString('ar-EG')
            });

            // Add property info
            tenantData.unshift({
                'المستأجر': 'معلومات العقار',
                'الإيرادات (ج.م)': `اسم العقار: ${property.name}`,
                'المصروفات (ج.م)': `عدد المستأجرين: ${property.tenantCount}`,
                'صافي الدخل (ج.م)': `تاريخ الإضافة: ${new Date(property.createdAt).toLocaleDateString('ar-EG')}`,
                'حالة الدفع': 'تقرير العقار',
                'التليفون': ''
            });

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(tenantData);

            // Set column widths
            ws['!cols'] = [
                { width: 20 }, // المستأجر
                { width: 15 }, // الإيرادات
                { width: 15 }, // المصروفات
                { width: 15 }, // صافي الدخل
                { width: 15 }, // حالة الدفع
                { width: 15 }  // التليفون
            ];

            XLSX.utils.book_append_sheet(wb, ws, 'تقرير العقار');

            // Save Excel file
            XLSX.writeFile(wb, `تقرير-عقار-${property.name}.xlsx`);
            showNotification(`تم تصدير تقرير العقار ${property.name}`, 'success');
        }

        // Auto-save every 30 seconds
        setInterval(() => {
            if (properties.length > 0) {
                saveData();
            }
        }, 30000);

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            console.log('بدء تهيئة البرنامج...');
            loadData();
            updateStatistics();
            displayProperties();

            // Initialize report selects with delay to ensure data is loaded
            setTimeout(() => {
                populateReportSelects();
                console.log('تم تهيئة البرنامج وتحديث قوائم التقارير بنجاح');
                console.log('عدد العقارات:', properties.length);
            }, 300);
        });
    </script>
</body>
</html>
