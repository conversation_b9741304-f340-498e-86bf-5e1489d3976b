plugins {
    id 'com.android.application'
}

android {
    namespace 'com.realestate.manager'
    compileSdk 34

    defaultConfig {
        applicationId "com.realestate.manager"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // إعدادات التوقيع
            signingConfig signingConfigs.debug
        }
        debug {
            debuggable true
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    // إعدادات إضافية للـ WebView
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }

    // تمكين ViewBinding (اختياري)
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.webkit:webkit:1.8.0'
    
    // مكتبات إضافية للملفات والتخزين
    implementation 'androidx.documentfile:documentfile:1.0.1'
    implementation 'androidx.preference:preference:1.2.1'
    
    // مكتبات الاختبار
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
