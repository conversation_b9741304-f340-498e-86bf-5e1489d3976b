{"logs": [{"outputFile": "com.lawoffice.adart3akar.app-mergeReleaseResources-38:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69e8a0e0d964fb1e9e45715fb440dd3e\\transformed\\material-1.12.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,602,700,799,919,1003,1060,1123,1214,1281,1340,1430,1493,1558,1622,1691,1753,1807,1922,1980,2041,2095,2168,2295,2381,2463,2562,2647,2731,2864,2939,3015,3148,3234,3315,3369,3421,3487,3560,3640,3711,3791,3862,3938,4017,4086,4193,4289,4367,4462,4558,4632,4707,4806,4857,4939,5006,5093,5183,5245,5309,5372,5439,5541,5646,5743,5845,5903,5959,6037,6123,6198", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,71,81,105,97,98,119,83,56,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77,85,74,72", "endOffsets": "263,337,409,491,597,695,794,914,998,1055,1118,1209,1276,1335,1425,1488,1553,1617,1686,1748,1802,1917,1975,2036,2090,2163,2290,2376,2458,2557,2642,2726,2859,2934,3010,3143,3229,3310,3364,3416,3482,3555,3635,3706,3786,3857,3933,4012,4081,4188,4284,4362,4457,4553,4627,4702,4801,4852,4934,5001,5088,5178,5240,5304,5367,5434,5536,5641,5738,5840,5898,5954,6032,6118,6193,6266"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3001,3075,3147,3229,3335,4151,4250,4370,4526,4583,4732,4823,4890,4949,5039,5102,5167,5231,5300,5362,5416,5531,5589,5650,5704,5777,5904,5990,6072,6171,6256,6340,6473,6548,6624,6757,6843,6924,6978,7030,7096,7169,7249,7320,7400,7471,7547,7626,7695,7802,7898,7976,8071,8167,8241,8316,8415,8466,8548,8615,8702,8792,8854,8918,8981,9048,9150,9255,9352,9454,9512,9649,9950,10036,10111", "endLines": "5,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "endColumns": "12,73,71,81,105,97,98,119,83,56,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77,85,74,72", "endOffsets": "313,3070,3142,3224,3330,3428,4245,4365,4449,4578,4641,4818,4885,4944,5034,5097,5162,5226,5295,5357,5411,5526,5584,5645,5699,5772,5899,5985,6067,6166,6251,6335,6468,6543,6619,6752,6838,6919,6973,7025,7091,7164,7244,7315,7395,7466,7542,7621,7690,7797,7893,7971,8066,8162,8236,8311,8410,8461,8543,8610,8697,8787,8849,8913,8976,9043,9145,9250,9347,9449,9507,9563,9722,10031,10106,10179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1e2dd47e9ebc8ebee00aea44406d9542\\transformed\\preference-1.2.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,263,344,486,655,739", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "172,258,339,481,650,734,816"}, "to": {"startLines": "48,51,113,115,121,122,123", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4454,4646,9568,9727,10285,10454,10538", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "4521,4727,9644,9864,10449,10533,10615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\412d4fb45e796eeac261181f9a418e93\\transformed\\appcompat-1.7.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,425,529,636,723,823,943,1021,1098,1189,1282,1377,1471,1571,1664,1759,1853,1944,2035,2115,2221,2322,2419,2528,2628,2738,2898,9869", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "420,524,631,718,818,938,1016,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2110,2216,2317,2414,2523,2623,2733,2893,2996,9945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\476525ada1d35df1ee329f5bd94fbe69\\transformed\\core-1.13.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "38,39,40,41,42,43,44,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3433,3527,3630,3727,3829,3931,4029,10184", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3522,3625,3722,3824,3926,4024,4146,10280"}}]}]}