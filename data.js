// Data Management System for Real Estate Management
class DataManager {
    constructor() {
        this.properties = this.loadData('properties') || [];
        this.tenants = this.loadData('tenants') || [];
        this.contracts = this.loadData('contracts') || [];
        this.payments = this.loadData('payments') || [];
        this.activities = this.loadData('activities') || [];
        
        // Initialize with sample data if empty
        if (this.properties.length === 0) {
            this.initializeSampleData();
        }
    }

    // Local Storage Methods
    saveData(key, data) {
        localStorage.setItem(key, JSON.stringify(data));
    }

    loadData(key) {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    }

    // Generate unique ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // Properties Management
    addProperty(propertyData) {
        const property = {
            id: this.generateId(),
            ...propertyData,
            status: 'available',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        this.properties.push(property);
        this.saveData('properties', this.properties);
        this.addActivity(`تم إضافة عقار جديد: ${property.name}`);
        return property;
    }

    updateProperty(id, updates) {
        const index = this.properties.findIndex(p => p.id === id);
        if (index !== -1) {
            this.properties[index] = {
                ...this.properties[index],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            this.saveData('properties', this.properties);
            this.addActivity(`تم تحديث العقار: ${this.properties[index].name}`);
            return this.properties[index];
        }
        return null;
    }

    deleteProperty(id) {
        const property = this.properties.find(p => p.id === id);
        if (property) {
            this.properties = this.properties.filter(p => p.id !== id);
            this.saveData('properties', this.properties);
            this.addActivity(`تم حذف العقار: ${property.name}`);
            return true;
        }
        return false;
    }

    getProperty(id) {
        return this.properties.find(p => p.id === id);
    }

    getAllProperties() {
        return this.properties;
    }

    getAvailableProperties() {
        return this.properties.filter(p => p.status === 'available');
    }

    getRentedProperties() {
        return this.properties.filter(p => p.status === 'rented');
    }

    // Tenants Management
    addTenant(tenantData) {
        const tenant = {
            id: this.generateId(),
            ...tenantData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        this.tenants.push(tenant);
        this.saveData('tenants', this.tenants);
        this.addActivity(`تم إضافة مستأجر جديد: ${tenant.name}`);
        return tenant;
    }

    updateTenant(id, updates) {
        const index = this.tenants.findIndex(t => t.id === id);
        if (index !== -1) {
            this.tenants[index] = {
                ...this.tenants[index],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            this.saveData('tenants', this.tenants);
            this.addActivity(`تم تحديث بيانات المستأجر: ${this.tenants[index].name}`);
            return this.tenants[index];
        }
        return null;
    }

    deleteTenant(id) {
        const tenant = this.tenants.find(t => t.id === id);
        if (tenant) {
            this.tenants = this.tenants.filter(t => t.id !== id);
            this.saveData('tenants', this.tenants);
            this.addActivity(`تم حذف المستأجر: ${tenant.name}`);
            return true;
        }
        return false;
    }

    getTenant(id) {
        return this.tenants.find(t => t.id === id);
    }

    getAllTenants() {
        return this.tenants;
    }

    // Contracts Management
    addContract(contractData) {
        const contract = {
            id: this.generateId(),
            contractNumber: `C-${Date.now()}`,
            ...contractData,
            status: 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        this.contracts.push(contract);
        this.saveData('contracts', this.contracts);
        
        // Update property status to rented
        this.updateProperty(contract.propertyId, { status: 'rented' });
        
        this.addActivity(`تم إنشاء عقد جديد رقم: ${contract.contractNumber}`);
        return contract;
    }

    updateContract(id, updates) {
        const index = this.contracts.findIndex(c => c.id === id);
        if (index !== -1) {
            this.contracts[index] = {
                ...this.contracts[index],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            this.saveData('contracts', this.contracts);
            this.addActivity(`تم تحديث العقد رقم: ${this.contracts[index].contractNumber}`);
            return this.contracts[index];
        }
        return null;
    }

    deleteContract(id) {
        const contract = this.contracts.find(c => c.id === id);
        if (contract) {
            this.contracts = this.contracts.filter(c => c.id !== id);
            this.saveData('contracts', this.contracts);
            
            // Update property status to available
            this.updateProperty(contract.propertyId, { status: 'available' });
            
            this.addActivity(`تم حذف العقد رقم: ${contract.contractNumber}`);
            return true;
        }
        return false;
    }

    getContract(id) {
        return this.contracts.find(c => c.id === id);
    }

    getAllContracts() {
        return this.contracts;
    }

    getActiveContracts() {
        return this.contracts.filter(c => c.status === 'active');
    }

    // Payments Management
    addPayment(paymentData) {
        const payment = {
            id: this.generateId(),
            ...paymentData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        this.payments.push(payment);
        this.saveData('payments', this.payments);
        this.addActivity(`تم تسجيل دفعة بمبلغ ${payment.amount} ر.س`);
        return payment;
    }

    updatePayment(id, updates) {
        const index = this.payments.findIndex(p => p.id === id);
        if (index !== -1) {
            this.payments[index] = {
                ...this.payments[index],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            this.saveData('payments', this.payments);
            return this.payments[index];
        }
        return null;
    }

    deletePayment(id) {
        const payment = this.payments.find(p => p.id === id);
        if (payment) {
            this.payments = this.payments.filter(p => p.id !== id);
            this.saveData('payments', this.payments);
            this.addActivity(`تم حذف دفعة بمبلغ ${payment.amount} ر.س`);
            return true;
        }
        return false;
    }

    getPayment(id) {
        return this.payments.find(p => p.id === id);
    }

    getAllPayments() {
        return this.payments;
    }

    getPaymentsByProperty(propertyId) {
        return this.payments.filter(p => p.propertyId === propertyId);
    }

    getPaymentsByTenant(tenantId) {
        return this.payments.filter(p => p.tenantId === tenantId);
    }

    // Activities Management
    addActivity(description) {
        const activity = {
            id: this.generateId(),
            description,
            timestamp: new Date().toISOString(),
            date: new Date().toLocaleDateString('ar-SA')
        };
        
        this.activities.unshift(activity); // Add to beginning
        
        // Keep only last 50 activities
        if (this.activities.length > 50) {
            this.activities = this.activities.slice(0, 50);
        }
        
        this.saveData('activities', this.activities);
        return activity;
    }

    getRecentActivities(limit = 10) {
        return this.activities.slice(0, limit);
    }

    // Statistics and Reports
    getStatistics() {
        const totalProperties = this.properties.length;
        const rentedProperties = this.getRentedProperties().length;
        const availableProperties = this.getAvailableProperties().length;
        
        const monthlyIncome = this.getActiveContracts().reduce((total, contract) => {
            return total + (parseFloat(contract.rentAmount) || 0);
        }, 0);

        return {
            totalProperties,
            rentedProperties,
            availableProperties,
            monthlyIncome,
            totalTenants: this.tenants.length,
            activeContracts: this.getActiveContracts().length,
            totalPayments: this.payments.length
        };
    }

    getMonthlyRevenue() {
        const monthlyData = {};
        
        this.payments.forEach(payment => {
            const date = new Date(payment.date);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            
            if (!monthlyData[monthKey]) {
                monthlyData[monthKey] = 0;
            }
            
            monthlyData[monthKey] += parseFloat(payment.amount) || 0;
        });

        return monthlyData;
    }

    // Search and Filter
    searchProperties(query) {
        const searchTerm = query.toLowerCase();
        return this.properties.filter(property => 
            property.name.toLowerCase().includes(searchTerm) ||
            property.address.toLowerCase().includes(searchTerm) ||
            property.type.toLowerCase().includes(searchTerm)
        );
    }

    filterProperties(status) {
        if (!status) return this.properties;
        return this.properties.filter(property => property.status === status);
    }

    // Initialize Sample Data
    initializeSampleData() {
        // Sample Properties
        const sampleProperties = [
            {
                name: 'شقة الياسمين',
                address: 'حي الملك فهد، الرياض',
                type: 'apartment',
                area: 120,
                rooms: 3,
                bathrooms: 2,
                rent: 2500,
                description: 'شقة مفروشة بالكامل مع إطلالة رائعة'
            },
            {
                name: 'فيلا الورود',
                address: 'حي النرجس، الرياض',
                type: 'villa',
                area: 400,
                rooms: 5,
                bathrooms: 4,
                rent: 8000,
                description: 'فيلا فاخرة مع حديقة وموقف سيارات'
            },
            {
                name: 'مكتب التجارة',
                address: 'شارع الملك عبدالعزيز، جدة',
                type: 'office',
                area: 80,
                rooms: 2,
                bathrooms: 1,
                rent: 3000,
                description: 'مكتب في موقع تجاري ممتاز'
            }
        ];

        sampleProperties.forEach(property => {
            this.addProperty(property);
        });

        // Sample Tenants
        const sampleTenants = [
            {
                name: 'أحمد محمد السعيد',
                phone: '0501234567',
                email: '<EMAIL>',
                nationalId: '1234567890'
            },
            {
                name: 'فاطمة علي الزهراني',
                phone: '0509876543',
                email: '<EMAIL>',
                nationalId: '0987654321'
            }
        ];

        sampleTenants.forEach(tenant => {
            this.addTenant(tenant);
        });

        // Sample Contract
        if (this.properties.length > 0 && this.tenants.length > 0) {
            this.addContract({
                propertyId: this.properties[0].id,
                tenantId: this.tenants[0].id,
                rentAmount: this.properties[0].rent,
                startDate: '2024-01-01',
                endDate: '2024-12-31',
                deposit: this.properties[0].rent * 2
            });
        }
    }
}

// Initialize Data Manager
const dataManager = new DataManager();
