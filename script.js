// Real Estate Management System - Main JavaScript File

// Global Variables
let currentSection = 'dashboard';
let currentModal = null;

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadDashboard();
});

// Initialize Application
function initializeApp() {
    // Set up navigation
    const navButtons = document.querySelectorAll('.nav-btn');
    navButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const section = this.dataset.section;
            switchSection(section);
        });
    });

    // Set up modal overlay click to close
    document.getElementById('modal-overlay').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Set up form submissions
    setupFormHandlers();
}

// Setup Event Listeners
function setupEventListeners() {
    // Property search
    const propertySearch = document.getElementById('property-search');
    if (propertySearch) {
        propertySearch.addEventListener('input', function() {
            filterProperties();
        });
    }

    // Property filter
    const propertyFilter = document.getElementById('property-filter');
    if (propertyFilter) {
        propertyFilter.addEventListener('change', function() {
            filterProperties();
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && currentModal) {
            closeModal();
        }
    });
}

// Section Navigation
function switchSection(sectionName) {
    // Update navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

    // Update sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });
    document.getElementById(sectionName).classList.add('active');

    currentSection = sectionName;

    // Load section data
    switch(sectionName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'properties':
            loadProperties();
            break;
        case 'tenants':
            loadTenants();
            break;
        case 'contracts':
            loadContracts();
            break;
        case 'payments':
            loadPayments();
            break;
        case 'reports':
            loadReports();
            break;
    }
}

// Dashboard Functions
function loadDashboard() {
    const stats = dataManager.getStatistics();

    // Update statistics
    document.getElementById('total-properties').textContent = stats.totalProperties;
    document.getElementById('rented-properties').textContent = stats.rentedProperties;
    document.getElementById('available-properties').textContent = stats.availableProperties;
    document.getElementById('monthly-income').textContent = `${stats.monthlyIncome.toLocaleString()} ر.س`;

    // Load recent activities
    loadRecentActivities();
}

function loadRecentActivities() {
    const activities = dataManager.getRecentActivities(10);
    const container = document.getElementById('recent-activities-list');

    if (activities.length === 0) {
        container.innerHTML = '<div class="activity-item">لا توجد أنشطة حديثة</div>';
        return;
    }

    container.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <strong>${activity.date}</strong> - ${activity.description}
        </div>
    `).join('');
}

// Properties Functions
function loadProperties() {
    const properties = dataManager.getAllProperties();
    displayProperties(properties);
}

function displayProperties(properties) {
    const container = document.getElementById('properties-grid');

    if (properties.length === 0) {
        container.innerHTML = '<div class="no-data">لا توجد عقارات مسجلة</div>';
        return;
    }

    container.innerHTML = properties.map(property => `
        <div class="property-card">
            <div class="property-image">
                <i class="fas fa-${getPropertyIcon(property.type)}"></i>
            </div>
            <div class="property-info">
                <h3 class="property-title">${property.name}</h3>
                <p class="property-address">${property.address}</p>
                <div class="property-details">
                    <span><i class="fas fa-expand-arrows-alt"></i> ${property.area} م²</span>
                    <span><i class="fas fa-bed"></i> ${property.rooms || 0} غرف</span>
                    <span><i class="fas fa-bath"></i> ${property.bathrooms || 0} حمام</span>
                </div>
                <div class="property-rent">${property.rent.toLocaleString()} ر.س/شهر</div>
                <span class="property-status status-${property.status}">
                    ${getStatusText(property.status)}
                </span>
                <div class="property-actions">
                    <button class="btn btn-primary" onclick="viewProperty('${property.id}')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-secondary" onclick="editProperty('${property.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-danger" onclick="deleteProperty('${property.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

function filterProperties() {
    const searchTerm = document.getElementById('property-search').value;
    const filterStatus = document.getElementById('property-filter').value;

    let properties = dataManager.getAllProperties();

    if (searchTerm) {
        properties = dataManager.searchProperties(searchTerm);
    }

    if (filterStatus) {
        properties = properties.filter(p => p.status === filterStatus);
    }

    displayProperties(properties);
}

function getPropertyIcon(type) {
    const icons = {
        apartment: 'building',
        villa: 'home',
        office: 'briefcase',
        shop: 'store'
    };
    return icons[type] || 'building';
}

function getStatusText(status) {
    const statusTexts = {
        available: 'متاح',
        rented: 'مؤجر',
        maintenance: 'تحت الصيانة'
    };
    return statusTexts[status] || status;
}

// Tenants Functions
function loadTenants() {
    const tenants = dataManager.getAllTenants();
    const tbody = document.querySelector('#tenants-table tbody');

    if (tenants.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد مستأجرين مسجلين</td></tr>';
        return;
    }

    tbody.innerHTML = tenants.map(tenant => {
        const contract = dataManager.getActiveContracts().find(c => c.tenantId === tenant.id);
        const property = contract ? dataManager.getProperty(contract.propertyId) : null;

        return `
            <tr>
                <td>${tenant.name}</td>
                <td>${tenant.phone}</td>
                <td>${tenant.email || '-'}</td>
                <td>${property ? property.name : 'غير مؤجر'}</td>
                <td>${contract ? new Date(contract.startDate).toLocaleDateString('ar-SA') : '-'}</td>
                <td>
                    <button class="btn btn-secondary" onclick="editTenant('${tenant.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger" onclick="deleteTenant('${tenant.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

// Contracts Functions
function loadContracts() {
    const contracts = dataManager.getAllContracts();
    const tbody = document.querySelector('#contracts-table tbody');

    if (contracts.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center">لا توجد عقود مسجلة</td></tr>';
        return;
    }

    tbody.innerHTML = contracts.map(contract => {
        const property = dataManager.getProperty(contract.propertyId);
        const tenant = dataManager.getTenant(contract.tenantId);

        return `
            <tr>
                <td>${contract.contractNumber}</td>
                <td>${property ? property.name : 'غير محدد'}</td>
                <td>${tenant ? tenant.name : 'غير محدد'}</td>
                <td>${contract.rentAmount.toLocaleString()} ر.س</td>
                <td>${new Date(contract.startDate).toLocaleDateString('ar-SA')}</td>
                <td>${new Date(contract.endDate).toLocaleDateString('ar-SA')}</td>
                <td>
                    <span class="property-status status-${contract.status}">
                        ${getContractStatusText(contract.status)}
                    </span>
                </td>
                <td>
                    <button class="btn btn-secondary" onclick="editContract('${contract.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger" onclick="deleteContract('${contract.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

function getContractStatusText(status) {
    const statusTexts = {
        active: 'نشط',
        expired: 'منتهي',
        terminated: 'ملغي'
    };
    return statusTexts[status] || status;
}

// Payments Functions
function loadPayments() {
    const payments = dataManager.getAllPayments();
    const tbody = document.querySelector('#payments-table tbody');

    if (payments.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center">لا توجد مدفوعات مسجلة</td></tr>';
        return;
    }

    tbody.innerHTML = payments.map(payment => {
        const property = dataManager.getProperty(payment.propertyId);
        const tenant = dataManager.getTenant(payment.tenantId);

        return `
            <tr>
                <td>${new Date(payment.date).toLocaleDateString('ar-SA')}</td>
                <td>${property ? property.name : 'غير محدد'}</td>
                <td>${tenant ? tenant.name : 'غير محدد'}</td>
                <td>${payment.amount.toLocaleString()} ر.س</td>
                <td>${getPaymentTypeText(payment.type)}</td>
                <td>${getPaymentMethodText(payment.method)}</td>
                <td>
                    <span class="property-status status-${payment.status}">
                        ${getPaymentStatusText(payment.status)}
                    </span>
                </td>
                <td>
                    <button class="btn btn-secondary" onclick="editPayment('${payment.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger" onclick="deletePayment('${payment.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

function getPaymentTypeText(type) {
    const types = {
        rent: 'إيجار',
        deposit: 'تأمين',
        maintenance: 'صيانة',
        utilities: 'خدمات'
    };
    return types[type] || type;
}

function getPaymentMethodText(method) {
    const methods = {
        cash: 'نقدي',
        bank: 'تحويل بنكي',
        check: 'شيك',
        card: 'بطاقة'
    };
    return methods[method] || method;
}

function getPaymentStatusText(status) {
    const statusTexts = {
        paid: 'مدفوع',
        pending: 'معلق',
        overdue: 'متأخر'
    };
    return statusTexts[status] || status;
}

// Reports Functions
function loadReports() {
    const monthlyRevenue = dataManager.getMonthlyRevenue();
    const stats = dataManager.getStatistics();

    // Display monthly revenue chart placeholder
    const monthlyChart = document.getElementById('monthly-revenue-chart');
    monthlyChart.innerHTML = `
        <div style="text-align: center; padding: 2rem;">
            <i class="fas fa-chart-line" style="font-size: 3rem; color: #667eea; margin-bottom: 1rem;"></i>
            <h4>الإيرادات الشهرية</h4>
            <p>إجمالي الإيرادات: ${Object.values(monthlyRevenue).reduce((a, b) => a + b, 0).toLocaleString()} ر.س</p>
        </div>
    `;

    // Display properties distribution chart placeholder
    const distributionChart = document.getElementById('properties-distribution-chart');
    distributionChart.innerHTML = `
        <div style="text-align: center; padding: 2rem;">
            <i class="fas fa-pie-chart" style="font-size: 3rem; color: #764ba2; margin-bottom: 1rem;"></i>
            <h4>توزيع العقارات</h4>
            <p>متاح: ${stats.availableProperties} | مؤجر: ${stats.rentedProperties}</p>
        </div>
    `;
}

// Modal Functions
function showModal(modalId) {
    currentModal = modalId;
    document.getElementById('modal-overlay').classList.add('active');
    document.getElementById(modalId).style.display = 'block';
}

function closeModal() {
    if (currentModal) {
        document.getElementById('modal-overlay').classList.remove('active');
        document.getElementById(currentModal).style.display = 'none';
        currentModal = null;
    }
}

function showAddPropertyModal() {
    showModal('add-property-modal');
}

function showAddTenantModal() {
    showModal('add-tenant-modal');
}

function showAddContractModal() {
    populateContractSelects();
    showModal('add-contract-modal');
}

function showPaymentModal() {
    populatePaymentSelects();
    showModal('add-payment-modal');
}

function populateContractSelects() {
    // Populate available properties
    const propertySelect = document.getElementById('contract-property');
    const availableProperties = dataManager.getAvailableProperties();

    propertySelect.innerHTML = '<option value="">اختر العقار</option>';
    availableProperties.forEach(property => {
        propertySelect.innerHTML += `<option value="${property.id}">${property.name}</option>`;
    });

    // Populate tenants
    const tenantSelect = document.getElementById('contract-tenant');
    const tenants = dataManager.getAllTenants();

    tenantSelect.innerHTML = '<option value="">اختر المستأجر</option>';
    tenants.forEach(tenant => {
        tenantSelect.innerHTML += `<option value="${tenant.id}">${tenant.name}</option>`;
    });
}

function populatePaymentSelects() {
    // Populate rented properties
    const propertySelect = document.getElementById('payment-property');
    const rentedProperties = dataManager.getRentedProperties();

    propertySelect.innerHTML = '<option value="">اختر العقار</option>';
    rentedProperties.forEach(property => {
        propertySelect.innerHTML += `<option value="${property.id}">${property.name}</option>`;
    });

    // Populate tenants
    const tenantSelect = document.getElementById('payment-tenant');
    const tenants = dataManager.getAllTenants();

    tenantSelect.innerHTML = '<option value="">اختر المستأجر</option>';
    tenants.forEach(tenant => {
        tenantSelect.innerHTML += `<option value="${tenant.id}">${tenant.name}</option>`;
    });
}

// Form Handlers
function setupFormHandlers() {
    // Add Property Form
    const addPropertyForm = document.getElementById('add-property-form');
    if (addPropertyForm) {
        addPropertyForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const propertyData = {
                name: formData.get('name'),
                address: formData.get('address'),
                type: formData.get('type'),
                area: parseInt(formData.get('area')),
                rooms: parseInt(formData.get('rooms')) || 0,
                bathrooms: parseInt(formData.get('bathrooms')) || 0,
                rent: parseFloat(formData.get('rent')),
                description: formData.get('description') || ''
            };

            dataManager.addProperty(propertyData);
            closeModal();
            this.reset();

            if (currentSection === 'properties') {
                loadProperties();
            }
            if (currentSection === 'dashboard') {
                loadDashboard();
            }

            showNotification('تم إضافة العقار بنجاح', 'success');
        });
    }

    // Add Tenant Form
    const addTenantForm = document.getElementById('add-tenant-form');
    if (addTenantForm) {
        addTenantForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const tenantData = {
                name: formData.get('name'),
                phone: formData.get('phone'),
                email: formData.get('email') || '',
                nationalId: formData.get('nationalId'),
                address: formData.get('address') || '',
                notes: formData.get('notes') || ''
            };

            dataManager.addTenant(tenantData);
            closeModal();
            this.reset();

            if (currentSection === 'tenants') {
                loadTenants();
            }
            if (currentSection === 'dashboard') {
                loadDashboard();
            }

            showNotification('تم إضافة المستأجر بنجاح', 'success');
        });
    }

    // Add Contract Form
    const addContractForm = document.getElementById('add-contract-form');
    if (addContractForm) {
        addContractForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const contractData = {
                propertyId: formData.get('propertyId'),
                tenantId: formData.get('tenantId'),
                rentAmount: parseFloat(formData.get('rentAmount')),
                deposit: parseFloat(formData.get('deposit')) || 0,
                startDate: formData.get('startDate'),
                endDate: formData.get('endDate'),
                terms: formData.get('terms') || ''
            };

            dataManager.addContract(contractData);
            closeModal();
            this.reset();

            if (currentSection === 'contracts') {
                loadContracts();
            }
            if (currentSection === 'properties') {
                loadProperties();
            }
            if (currentSection === 'dashboard') {
                loadDashboard();
            }

            showNotification('تم إنشاء العقد بنجاح', 'success');
        });
    }

    // Add Payment Form
    const addPaymentForm = document.getElementById('add-payment-form');
    if (addPaymentForm) {
        addPaymentForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const paymentData = {
                propertyId: formData.get('propertyId'),
                tenantId: formData.get('tenantId'),
                amount: parseFloat(formData.get('amount')),
                date: formData.get('date'),
                type: formData.get('type'),
                method: formData.get('method'),
                status: 'paid',
                notes: formData.get('notes') || ''
            };

            dataManager.addPayment(paymentData);
            closeModal();
            this.reset();

            if (currentSection === 'payments') {
                loadPayments();
            }
            if (currentSection === 'dashboard') {
                loadDashboard();
            }

            showNotification('تم تسجيل الدفعة بنجاح', 'success');
        });
    }
}

// Property Actions
function viewProperty(id) {
    const property = dataManager.getProperty(id);
    if (property) {
        alert(`تفاصيل العقار:\n\nالاسم: ${property.name}\nالعنوان: ${property.address}\nالنوع: ${property.type}\nالمساحة: ${property.area} م²\nالإيجار: ${property.rent} ر.س`);
    }
}

function editProperty(id) {
    alert('سيتم إضافة وظيفة التعديل قريباً');
}

function deleteProperty(id) {
    if (confirm('هل أنت متأكد من حذف هذا العقار؟')) {
        dataManager.deleteProperty(id);
        loadProperties();
        if (currentSection === 'dashboard') {
            loadDashboard();
        }
        showNotification('تم حذف العقار بنجاح', 'success');
    }
}

// Tenant Actions
function editTenant(id) {
    alert('سيتم إضافة وظيفة التعديل قريباً');
}

function deleteTenant(id) {
    if (confirm('هل أنت متأكد من حذف هذا المستأجر؟')) {
        dataManager.deleteTenant(id);
        loadTenants();
        showNotification('تم حذف المستأجر بنجاح', 'success');
    }
}

// Contract Actions
function editContract(id) {
    alert('سيتم إضافة وظيفة التعديل قريباً');
}

function deleteContract(id) {
    if (confirm('هل أنت متأكد من حذف هذا العقد؟')) {
        dataManager.deleteContract(id);
        loadContracts();
        if (currentSection === 'dashboard') {
            loadDashboard();
        }
        showNotification('تم حذف العقد بنجاح', 'success');
    }
}

// Payment Actions
function editPayment(id) {
    alert('سيتم إضافة وظيفة التعديل قريباً');
}

function deletePayment(id) {
    if (confirm('هل أنت متأكد من حذف هذه الدفعة؟')) {
        dataManager.deletePayment(id);
        loadPayments();
        showNotification('تم حذف الدفعة بنجاح', 'success');
    }
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        ${message}
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
