<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العقارات المتقدم</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- PDF and Excel Export Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        /* Main Content */
        .main-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        /* Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
        }

        .stat-card p {
            opacity: 0.9;
        }

        /* Add Property Section */
        .add-property-section {
            background: #f8f9ff;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border: 2px solid #e0e6ff;
        }

        .section-title {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e6ff;
            border-radius: 10px;
            font-family: inherit;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        /* Tenants List */
        .tenants-container {
            margin-top: 20px;
            display: none;
        }

        .tenant-item {
            background: white;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .tenant-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .tenant-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        /* Buttons */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #51cf66, #40c057);
            color: white;
            box-shadow: 0 4px 15px rgba(81, 207, 102, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(81, 207, 102, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        /* Properties List */
        .properties-list {
            margin-top: 30px;
        }

        .property-card {
            background: white;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .property-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .property-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .property-title {
            font-size: 1.5rem;
            color: #333;
            font-weight: 700;
        }

        .property-actions {
            display: flex;
            gap: 10px;
        }

        .tenants-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .tenant-card {
            background: #f8f9ff;
            border: 1px solid #e0e6ff;
            border-radius: 10px;
            padding: 15px;
        }

        .tenant-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .tenant-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9rem;
            color: #666;
        }

        .status-paid {
            color: #51cf66;
            font-weight: 600;
        }

        .status-unpaid {
            color: #ff6b6b;
            font-weight: 600;
        }

        .status-partial {
            color: #ffd43b;
            font-weight: 600;
        }

        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 1000;
            transform: translateX(400px);
            opacity: 0;
            transition: all 0.3s ease;
            min-width: 300px;
            border-left: 4px solid #667eea;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification-success {
            border-left-color: #51cf66;
            color: #2b8a3e;
        }

        .notification-error {
            border-left-color: #ff6b6b;
            color: #c92a2a;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .tenant-fields {
                grid-template-columns: 1fr;
            }

            .tenants-grid {
                grid-template-columns: 1fr;
            }

            .property-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .property-actions {
                width: 100%;
                justify-content: center;
            }

            .reports-grid {
                grid-template-columns: 1fr;
            }

            .report-controls {
                gap: 8px;
            }

            .report-controls .btn {
                font-size: 0.8rem;
                padding: 6px 10px;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .header .container {
                flex-direction: column;
                gap: 15px;
            }

            .nav {
                flex-wrap: wrap;
                justify-content: center;
                gap: 5px;
            }

            .nav-btn {
                font-size: 0.8rem;
                padding: 8px 12px;
            }
        }

        /* Export/Import Section */
        .data-management {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .data-management h3 {
            color: #856404;
            margin-bottom: 15px;
        }

        .data-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffd43b, #fab005);
            color: #856404;
            box-shadow: 0 4px 15px rgba(255, 212, 59, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #74c0fc, #339af0);
            color: white;
            box-shadow: 0 4px 15px rgba(116, 192, 252, 0.3);
        }

        #fileInput {
            display: none;
        }

        /* Reports Section */
        .reports-section {
            background: #f8f9ff;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border: 2px solid #e0e6ff;
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .report-card {
            background: white;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .report-card h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .report-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .report-controls .btn {
            font-size: 0.9rem;
            padding: 8px 12px;
        }

        .btn-pdf {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .btn-excel {
            background: linear-gradient(135deg, #28a745, #218838);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .report-controls select {
            padding: 8px 12px;
            border: 2px solid #e0e6ff;
            border-radius: 8px;
            font-family: inherit;
        }

        .report-display {
            background: white;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .report-content {
            line-height: 1.6;
        }

        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .report-table th,
        .report-table td {
            padding: 12px;
            text-align: right;
            border: 1px solid #e0e6ff;
        }

        .report-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }

        .report-table tr:nth-child(even) {
            background: #f8f9ff;
        }

        .report-summary {
            background: #f8f9ff;
            border: 2px solid #e0e6ff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .summary-item:last-child {
            margin-bottom: 0;
            border-top: 2px solid #667eea;
            padding-top: 10px;
            color: #667eea;
            font-size: 1.1rem;
        }

        .expense-item {
            color: #ff6b6b;
        }

        .income-item {
            color: #51cf66;
        }

        .net-item {
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-building"></i> نظام إدارة العقارات المتقدم</h1>
            <p>إدارة شاملة للعقارات والمستأجرين مع حفظ البيانات المحمول</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-building"></i>
                <h3 id="totalProperties">0</h3>
                <p>إجمالي العقارات</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-users"></i>
                <h3 id="totalTenants">0</h3>
                <p>إجمالي المستأجرين</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-dollar-sign"></i>
                <h3 id="totalIncome">0 ج.م</h3>
                <p>صافي الدخل الشهري</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-check-circle"></i>
                <h3 id="paidTenants">0</h3>
                <p>المستأجرين المدفوعين</p>
            </div>
        </div>

        <!-- Data Management -->
        <div class="data-management">
            <h3><i class="fas fa-database"></i> إدارة البيانات</h3>
            <div class="data-buttons">
                <button class="btn btn-info" onclick="exportData()">
                    <i class="fas fa-download"></i> تصدير البيانات
                </button>
                <button class="btn btn-warning" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-upload"></i> استيراد البيانات
                </button>
                <button class="btn btn-danger" onclick="clearAllData()">
                    <i class="fas fa-trash"></i> مسح جميع البيانات
                </button>
            </div>
            <input type="file" id="fileInput" accept=".json" onchange="importData(event)">
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Add Property Form -->
            <div class="add-property-section">
                <h2 class="section-title">
                    <i class="fas fa-plus-circle"></i> إضافة عقار جديد
                </h2>

                <form id="propertyForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="propertyName">اسم العقار</label>
                            <input type="text" id="propertyName" required placeholder="أدخل اسم العقار">
                        </div>
                        <div class="form-group">
                            <label for="tenantCount">عدد المستأجرين</label>
                            <input type="number" id="tenantCount" min="1" max="50" required placeholder="عدد المستأجرين">
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="button" class="btn btn-primary" onclick="generateTenantFields()">
                            <i class="fas fa-users"></i> إنشاء قوائم المستأجرين
                        </button>
                    </div>

                    <!-- Tenants Container -->
                    <div id="tenantsContainer" class="tenants-container"></div>

                    <div class="form-group" id="saveButtonContainer" style="display: none;">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> حفظ العقار والمستأجرين
                        </button>
                    </div>
                </form>
            </div>

            <!-- Reports Section -->
            <div class="reports-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-bar"></i> التقارير والإحصائيات
                </h2>

                <div class="reports-grid">
                    <div class="report-card">
                        <h3><i class="fas fa-calendar-alt"></i> التقارير الشهرية</h3>
                        <div class="report-controls">
                            <select id="reportMonth">
                                <option value="">اختر الشهر</option>
                                <option value="01">يناير</option>
                                <option value="02">فبراير</option>
                                <option value="03">مارس</option>
                                <option value="04">أبريل</option>
                                <option value="05">مايو</option>
                                <option value="06">يونيو</option>
                                <option value="07">يوليو</option>
                                <option value="08">أغسطس</option>
                                <option value="09">سبتمبر</option>
                                <option value="10">أكتوبر</option>
                                <option value="11">نوفمبر</option>
                                <option value="12">ديسمبر</option>
                            </select>
                            <select id="reportYear">
                                <option value="">اختر السنة</option>
                            </select>
                            <button class="btn btn-info" onclick="generateMonthlyReport()">
                                <i class="fas fa-file-alt"></i> إنشاء التقرير
                            </button>
                            <button class="btn btn-success" onclick="exportMonthlyReport()">
                                <i class="fas fa-download"></i> رفع JSON
                            </button>
                            <button class="btn btn-danger" onclick="exportMonthlyReportPDF()">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                            <button class="btn btn-info" onclick="exportMonthlyReportExcel()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                    </div>

                    <div class="report-card">
                        <h3><i class="fas fa-user-chart"></i> تقارير المستأجرين</h3>
                        <div class="report-controls">
                            <select id="tenantSelect">
                                <option value="">اختر المستأجر</option>
                            </select>
                            <select id="tenantReportYear">
                                <option value="">اختر السنة</option>
                            </select>
                            <button class="btn btn-info" onclick="generateTenantReport()">
                                <i class="fas fa-user"></i> تقرير المستأجر
                            </button>
                            <button class="btn btn-danger" onclick="exportTenantReportPDF()">
                                <i class="fas fa-file-pdf"></i> PDF
                            </button>
                            <button class="btn btn-success" onclick="exportTenantReportExcel()">
                                <i class="fas fa-file-excel"></i> Excel
                            </button>
                        </div>
                    </div>

                    <div class="report-card">
                        <h3><i class="fas fa-building"></i> تقارير العقارات</h3>
                        <div class="report-controls">
                            <select id="propertySelect">
                                <option value="">اختر العقار</option>
                            </select>
                            <button class="btn btn-info" onclick="generatePropertyReport()">
                                <i class="fas fa-chart-line"></i> تقرير العقار
                            </button>
                            <button class="btn btn-danger" onclick="exportPropertyReportPDF()">
                                <i class="fas fa-file-pdf"></i> PDF
                            </button>
                            <button class="btn btn-success" onclick="exportPropertyReportExcel()">
                                <i class="fas fa-file-excel"></i> Excel
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Report Display Area -->
                <div id="reportDisplay" class="report-display" style="display: none;">
                    <div class="report-header">
                        <h3 id="reportTitle"></h3>
                        <button class="btn btn-secondary" onclick="closeReport()">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                    <div id="reportContent"></div>
                </div>
            </div>

            <!-- Properties List -->
            <div class="properties-list">
                <h2 class="section-title">
                    <i class="fas fa-list"></i> قائمة العقارات
                </h2>
                <div id="propertiesList"></div>
            </div>
        </div>
    </div>

    <script>
        // Global Variables
        let properties = [];
        let currentPropertyId = null;

        // Initialize App
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            updateStatistics();
            displayProperties();
            populateReportSelects();

            // Setup form submission
            document.getElementById('propertyForm').addEventListener('submit', saveProperty);
        });

        // Generate Tenant Fields
        function generateTenantFields() {
            const propertyName = document.getElementById('propertyName').value.trim();
            const tenantCount = parseInt(document.getElementById('tenantCount').value);

            if (!propertyName) {
                showNotification('يرجى إدخال اسم العقار أولاً', 'error');
                return;
            }

            if (!tenantCount || tenantCount < 1 || tenantCount > 50) {
                showNotification('يرجى إدخال عدد صحيح للمستأجرين (1-50)', 'error');
                return;
            }

            const container = document.getElementById('tenantsContainer');
            container.innerHTML = '';
            container.style.display = 'block';

            for (let i = 1; i <= tenantCount; i++) {
                const tenantDiv = document.createElement('div');
                tenantDiv.className = 'tenant-item';
                tenantDiv.innerHTML = `
                    <div class="tenant-header">
                        <i class="fas fa-user"></i> المستأجر رقم ${i}
                    </div>
                    <div class="tenant-fields">
                        <div class="form-group">
                            <label>اسم المستأجر</label>
                            <input type="text" name="tenantName_${i}" placeholder="اسم المستأجر" required>
                        </div>
                        <div class="form-group">
                            <label>مبلغ الإيجار (ج.م)</label>
                            <input type="number" name="rentAmount_${i}" placeholder="0" min="0" step="0.01">
                        </div>
                        <div class="form-group">
                            <label>المصروفات الشهرية (ج.م)</label>
                            <input type="number" name="monthlyExpenses_${i}" placeholder="0" min="0" step="0.01">
                        </div>
                        <div class="form-group">
                            <label>حالة الدفع</label>
                            <select name="paymentStatus_${i}">
                                <option value="">اختر الحالة</option>
                                <option value="paid">مدفوع</option>
                                <option value="unpaid">غير مدفوع</option>
                                <option value="partial">مدفوع جزئياً</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>تاريخ الدفع</label>
                            <input type="date" name="paymentDate_${i}">
                        </div>
                        <div class="form-group">
                            <label>رقم التليفون</label>
                            <input type="tel" name="phone_${i}" placeholder="05xxxxxxxx">
                        </div>
                        <div class="form-group">
                            <label>ملاحظات</label>
                            <textarea name="notes_${i}" placeholder="ملاحظات إضافية" rows="2"></textarea>
                        </div>
                    </div>
                `;
                container.appendChild(tenantDiv);
            }

            document.getElementById('saveButtonContainer').style.display = 'block';
            showNotification(`تم إنشاء ${tenantCount} قائمة للمستأجرين`, 'success');
        }

        // Save Property
        function saveProperty(e) {
            e.preventDefault();

            const propertyName = document.getElementById('propertyName').value.trim();
            const tenantCount = parseInt(document.getElementById('tenantCount').value);

            if (!propertyName || !tenantCount) {
                showNotification('يرجى ملء جميع البيانات المطلوبة', 'error');
                return;
            }

            const tenants = [];
            let totalIncome = 0;

            // Collect tenant data
            for (let i = 1; i <= tenantCount; i++) {
                const tenantName = document.querySelector(`input[name="tenantName_${i}"]`).value.trim();
                const rentAmount = parseFloat(document.querySelector(`input[name="rentAmount_${i}"]`).value) || 0;
                const monthlyExpenses = parseFloat(document.querySelector(`input[name="monthlyExpenses_${i}"]`).value) || 0;
                const paymentStatus = document.querySelector(`select[name="paymentStatus_${i}"]`).value;
                const paymentDate = document.querySelector(`input[name="paymentDate_${i}"]`).value;
                const phone = document.querySelector(`input[name="phone_${i}"]`).value.trim();
                const notes = document.querySelector(`textarea[name="notes_${i}"]`).value.trim();

                // Allow empty data - only require tenant name if any data is entered
                if (tenantName || rentAmount > 0 || monthlyExpenses > 0 || paymentStatus || paymentDate || phone || notes) {
                    tenants.push({
                        id: generateId(),
                        name: tenantName || `مستأجر ${i}`,
                        rentAmount: rentAmount,
                        monthlyExpenses: monthlyExpenses,
                        paymentStatus: paymentStatus || 'unpaid',
                        paymentDate: paymentDate,
                        phone: phone,
                        notes: notes,
                        createdAt: new Date().toISOString()
                    });

                    if (paymentStatus === 'paid') {
                        totalIncome += rentAmount;
                    } else if (paymentStatus === 'partial') {
                        totalIncome += rentAmount * 0.5; // Assume 50% for partial payment
                    }
                } else {
                    // Add empty tenant slot for future completion
                    tenants.push({
                        id: generateId(),
                        name: `مستأجر ${i}`,
                        rentAmount: 0,
                        monthlyExpenses: 0,
                        paymentStatus: 'unpaid',
                        paymentDate: '',
                        phone: '',
                        notes: '',
                        isEmpty: true,
                        createdAt: new Date().toISOString()
                    });
                }
            }

            // Create property object
            const property = {
                id: generateId(),
                name: propertyName,
                tenantCount: tenantCount,
                tenants: tenants,
                totalIncome: totalIncome,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Add to properties array
            properties.push(property);

            // Save to localStorage
            saveData();

            // Update UI
            updateStatistics();
            displayProperties();

            // Reset form
            document.getElementById('propertyForm').reset();
            document.getElementById('tenantsContainer').style.display = 'none';
            document.getElementById('saveButtonContainer').style.display = 'none';

            showNotification(`تم حفظ العقار "${propertyName}" مع ${tenantCount} مستأجر بنجاح`, 'success');
        }

        // Display Properties
        function displayProperties() {
            const container = document.getElementById('propertiesList');

            if (properties.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 50px; color: #999;">
                        <i class="fas fa-home" style="font-size: 4rem; margin-bottom: 20px; opacity: 0.3;"></i>
                        <h3>لا توجد عقارات مسجلة</h3>
                        <p>قم بإضافة عقار جديد لبدء الاستخدام</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = properties.map(property => `
                <div class="property-card">
                    <div class="property-header">
                        <div class="property-title">
                            <i class="fas fa-building"></i> ${property.name}
                        </div>
                        <div class="property-actions">
                            <button class="btn btn-primary" onclick="editProperty('${property.id}')">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-danger" onclick="deleteProperty('${property.id}')">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px; color: #666;">
                        <strong>عدد المستأجرين:</strong> ${property.tenantCount} |
                        <strong>إجمالي الدخل:</strong> ${property.totalIncome.toLocaleString()} ج.م |
                        <strong>تاريخ الإضافة:</strong> ${new Date(property.createdAt).toLocaleDateString('ar-EG')}
                    </div>

                    <div class="tenants-grid">
                        ${property.tenants.map(tenant => `
                            <div class="tenant-card">
                                <div class="tenant-name">
                                    <i class="fas fa-user"></i> ${tenant.name}
                                </div>
                                <div class="tenant-info">
                                    <div><strong>الإيجار:</strong> ${tenant.rentAmount.toLocaleString()} ج.م</div>
                                    <div><strong>المصروفات:</strong> ${tenant.monthlyExpenses ? tenant.monthlyExpenses.toLocaleString() : '0'} ج.م</div>
                                    <div><strong>الحالة:</strong> <span class="status-${tenant.paymentStatus}">${getStatusText(tenant.paymentStatus)}</span></div>
                                    <div><strong>التاريخ:</strong> ${tenant.paymentDate ? new Date(tenant.paymentDate).toLocaleDateString('ar-EG') : 'غير محدد'}</div>
                                    <div><strong>التليفون:</strong> ${tenant.phone || 'غير محدد'}</div>
                                    <div><strong>صافي الدخل:</strong> <span class="net-item">${(tenant.rentAmount - (tenant.monthlyExpenses || 0)).toLocaleString()} ج.م</span></div>
                                </div>
                                ${tenant.notes ? `<div style="margin-top: 10px; font-size: 0.85rem; color: #666; font-style: italic;"><strong>ملاحظات:</strong> ${tenant.notes}</div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');
        }

        // Update Statistics
        function updateStatistics() {
            const totalProperties = properties.length;
            const totalTenants = properties.reduce((sum, property) => sum + property.tenantCount, 0);

            let totalIncome = 0;
            let totalExpenses = 0;
            let paidTenants = 0;

            properties.forEach(property => {
                property.tenants.forEach(tenant => {
                    if (!tenant.isEmpty) {
                        const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                                     tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                        const expenses = tenant.monthlyExpenses || 0;

                        totalIncome += income;
                        totalExpenses += expenses;

                        if (tenant.paymentStatus === 'paid') {
                            paidTenants++;
                        }
                    }
                });
            });

            const netIncome = totalIncome - totalExpenses;

            document.getElementById('totalProperties').textContent = totalProperties;
            document.getElementById('totalTenants').textContent = totalTenants;
            document.getElementById('totalIncome').textContent = `${netIncome.toLocaleString()} ج.م`;
            document.getElementById('paidTenants').textContent = paidTenants;

            // Update report selects when statistics change
            populateReportSelects();
        }

        // Edit Property
        function editProperty(propertyId) {
            const property = properties.find(p => p.id === propertyId);
            if (!property) return;

            // Fill form with existing data
            document.getElementById('propertyName').value = property.name;
            document.getElementById('tenantCount').value = property.tenantCount;

            // Generate tenant fields
            generateTenantFields();

            // Fill tenant data
            setTimeout(() => {
                property.tenants.forEach((tenant, index) => {
                    const i = index + 1;
                    document.querySelector(`input[name="tenantName_${i}"]`).value = tenant.name;
                    document.querySelector(`input[name="rentAmount_${i}"]`).value = tenant.rentAmount;
                    document.querySelector(`input[name="monthlyExpenses_${i}"]`).value = tenant.monthlyExpenses || 0;
                    document.querySelector(`select[name="paymentStatus_${i}"]`).value = tenant.paymentStatus;
                    document.querySelector(`input[name="paymentDate_${i}"]`).value = tenant.paymentDate;
                    document.querySelector(`input[name="phone_${i}"]`).value = tenant.phone;
                    document.querySelector(`textarea[name="notes_${i}"]`).value = tenant.notes;
                });
            }, 100);

            // Remove old property and set current ID for update
            properties = properties.filter(p => p.id !== propertyId);
            currentPropertyId = propertyId;

            // Scroll to form
            document.querySelector('.add-property-section').scrollIntoView({ behavior: 'smooth' });

            showNotification('تم تحميل بيانات العقار للتعديل', 'success');
        }

        // Delete Property
        function deleteProperty(propertyId) {
            const property = properties.find(p => p.id === propertyId);
            if (!property) return;

            if (confirm(`هل أنت متأكد من حذف العقار "${property.name}" وجميع بيانات المستأجرين؟`)) {
                properties = properties.filter(p => p.id !== propertyId);
                saveData();
                updateStatistics();
                displayProperties();
                showNotification('تم حذف العقار بنجاح', 'success');
            }
        }

        // Utility Functions
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        function getStatusText(status) {
            const statusTexts = {
                'paid': 'مدفوع',
                'unpaid': 'غير مدفوع',
                'partial': 'مدفوع جزئياً'
            };
            return statusTexts[status] || status;
        }

        // Data Management Functions
        function saveData() {
            const data = {
                properties: properties,
                timestamp: new Date().toISOString(),
                version: '1.0'
            };
            localStorage.setItem('realEstateData', JSON.stringify(data));
        }

        function loadData() {
            try {
                const savedData = localStorage.getItem('realEstateData');
                if (savedData) {
                    const data = JSON.parse(savedData);
                    properties = data.properties || [];
                }
            } catch (error) {
                console.error('Error loading data:', error);
                properties = [];
            }
        }

        function exportData() {
            const data = {
                properties: properties,
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `real-estate-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم تصدير البيانات بنجاح', 'success');
        }

        function importData(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    if (data.properties && Array.isArray(data.properties)) {
                        if (confirm('هل تريد استبدال البيانات الحالية أم إضافة البيانات الجديدة؟\n\nاضغط "موافق" للاستبدال أو "إلغاء" للإضافة')) {
                            properties = data.properties;
                        } else {
                            properties = [...properties, ...data.properties];
                        }

                        saveData();
                        updateStatistics();
                        displayProperties();
                        showNotification('تم استيراد البيانات بنجاح', 'success');
                    } else {
                        showNotification('ملف البيانات غير صحيح', 'error');
                    }
                } catch (error) {
                    showNotification('خطأ في قراءة الملف', 'error');
                }
            };
            reader.readAsText(file);

            // Reset file input
            event.target.value = '';
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                properties = [];
                localStorage.removeItem('realEstateData');
                updateStatistics();
                displayProperties();
                showNotification('تم مسح جميع البيانات', 'success');
            }
        }

        // Notification System
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Reports Functions
        function populateReportSelects() {
            // Populate years - نطاق واسع من 2020 إلى 2035
            const currentYear = new Date().getFullYear();
            const years = [];
            for (let year = 2020; year <= 2035; year++) {
                years.push(year);
            }

            const yearSelects = ['reportYear', 'tenantReportYear'];
            yearSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    select.innerHTML = '<option value="">اختر السنة</option>';
                    years.forEach(year => {
                        select.innerHTML += `<option value="${year}">${year}</option>`;
                    });
                }
            });

            // Populate tenants
            const tenantSelect = document.getElementById('tenantSelect');
            if (tenantSelect) {
                tenantSelect.innerHTML = '<option value="">اختر المستأجر</option>';
                properties.forEach(property => {
                    property.tenants.forEach(tenant => {
                        if (!tenant.isEmpty) {
                            tenantSelect.innerHTML += `<option value="${tenant.id}">${tenant.name} - ${property.name}</option>`;
                        }
                    });
                });
            }

            // Populate properties
            const propertySelect = document.getElementById('propertySelect');
            if (propertySelect) {
                propertySelect.innerHTML = '<option value="">اختر العقار</option>';
                properties.forEach(property => {
                    propertySelect.innerHTML += `<option value="${property.id}">${property.name}</option>`;
                });
            }
        }

        function generateMonthlyReport() {
            const month = document.getElementById('reportMonth').value;
            const year = document.getElementById('reportYear').value;

            if (!month || !year) {
                showNotification('يرجى اختيار الشهر والسنة', 'error');
                return;
            }

            const monthNames = {
                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            };

            let totalIncome = 0;
            let totalExpenses = 0;
            let reportData = [];

            properties.forEach(property => {
                property.tenants.forEach(tenant => {
                    if (!tenant.isEmpty) {
                        const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                                     tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                        const expenses = tenant.monthlyExpenses || 0;

                        totalIncome += income;
                        totalExpenses += expenses;

                        reportData.push({
                            property: property.name,
                            tenant: tenant.name,
                            income: income,
                            expenses: expenses,
                            net: income - expenses,
                            status: tenant.paymentStatus
                        });
                    }
                });
            });

            const reportContent = `
                <div class="report-summary">
                    <div class="summary-item income-item">
                        <span>إجمالي الإيرادات:</span>
                        <span>${totalIncome.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item expense-item">
                        <span>إجمالي المصروفات:</span>
                        <span>${totalExpenses.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item net-item">
                        <span>صافي الدخل:</span>
                        <span>${(totalIncome - totalExpenses).toLocaleString()} ج.م</span>
                    </div>
                </div>

                <table class="report-table">
                    <thead>
                        <tr>
                            <th>العقار</th>
                            <th>المستأجر</th>
                            <th>الإيرادات</th>
                            <th>المصروفات</th>
                            <th>صافي الدخل</th>
                            <th>حالة الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${reportData.map(row => `
                            <tr>
                                <td>${row.property}</td>
                                <td>${row.tenant}</td>
                                <td class="income-item">${row.income.toLocaleString()} ج.م</td>
                                <td class="expense-item">${row.expenses.toLocaleString()} ج.م</td>
                                <td class="net-item">${row.net.toLocaleString()} ج.م</td>
                                <td><span class="status-${row.status}">${getStatusText(row.status)}</span></td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            showReport(`تقرير شهر ${monthNames[month]} ${year}`, reportContent);
        }

        function generateTenantReport() {
            const tenantId = document.getElementById('tenantSelect').value;
            const year = document.getElementById('tenantReportYear').value;

            if (!tenantId || !year) {
                showNotification('يرجى اختيار المستأجر والسنة', 'error');
                return;
            }

            let tenant = null;
            let property = null;

            properties.forEach(prop => {
                const foundTenant = prop.tenants.find(t => t.id === tenantId);
                if (foundTenant) {
                    tenant = foundTenant;
                    property = prop;
                }
            });

            if (!tenant) {
                showNotification('المستأجر غير موجود', 'error');
                return;
            }

            const monthlyData = [];
            for (let month = 1; month <= 12; month++) {
                const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                             tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                const expenses = tenant.monthlyExpenses || 0;

                monthlyData.push({
                    month: month,
                    income: income,
                    expenses: expenses,
                    net: income - expenses
                });
            }

            const totalYearIncome = monthlyData.reduce((sum, data) => sum + data.income, 0);
            const totalYearExpenses = monthlyData.reduce((sum, data) => sum + data.expenses, 0);

            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            const reportContent = `
                <div style="margin-bottom: 20px;">
                    <p><strong>العقار:</strong> ${property.name}</p>
                    <p><strong>المستأجر:</strong> ${tenant.name}</p>
                    <p><strong>رقم التليفون:</strong> ${tenant.phone || 'غير محدد'}</p>
                    <p><strong>السنة:</strong> ${year}</p>
                </div>

                <div class="report-summary">
                    <div class="summary-item income-item">
                        <span>إجمالي الإيرادات السنوية:</span>
                        <span>${totalYearIncome.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item expense-item">
                        <span>إجمالي المصروفات السنوية:</span>
                        <span>${totalYearExpenses.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item net-item">
                        <span>صافي الدخل السنوي:</span>
                        <span>${(totalYearIncome - totalYearExpenses).toLocaleString()} ج.م</span>
                    </div>
                </div>

                <table class="report-table">
                    <thead>
                        <tr>
                            <th>الشهر</th>
                            <th>الإيرادات</th>
                            <th>المصروفات</th>
                            <th>صافي الدخل</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${monthlyData.map((data, index) => `
                            <tr>
                                <td>${monthNames[index]}</td>
                                <td class="income-item">${data.income.toLocaleString()} ج.م</td>
                                <td class="expense-item">${data.expenses.toLocaleString()} ج.م</td>
                                <td class="net-item">${data.net.toLocaleString()} ج.م</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            showReport(`التقرير السنوي للمستأجر ${tenant.name} - ${year}`, reportContent);
        }

        function generatePropertyReport() {
            const propertyId = document.getElementById('propertySelect').value;

            if (!propertyId) {
                showNotification('يرجى اختيار العقار', 'error');
                return;
            }

            const property = properties.find(p => p.id === propertyId);
            if (!property) {
                showNotification('العقار غير موجود', 'error');
                return;
            }

            let totalIncome = 0;
            let totalExpenses = 0;
            let paidTenants = 0;
            let unpaidTenants = 0;

            const tenantData = property.tenants.map(tenant => {
                if (!tenant.isEmpty) {
                    const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                                 tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                    const expenses = tenant.monthlyExpenses || 0;

                    totalIncome += income;
                    totalExpenses += expenses;

                    if (tenant.paymentStatus === 'paid') paidTenants++;
                    else if (tenant.paymentStatus === 'unpaid') unpaidTenants++;

                    return {
                        name: tenant.name,
                        income: income,
                        expenses: expenses,
                        net: income - expenses,
                        status: tenant.paymentStatus,
                        phone: tenant.phone
                    };
                }
                return null;
            }).filter(t => t !== null);

            const reportContent = `
                <div style="margin-bottom: 20px;">
                    <p><strong>اسم العقار:</strong> ${property.name}</p>
                    <p><strong>عدد المستأجرين:</strong> ${property.tenantCount}</p>
                    <p><strong>المستأجرين المدفوعين:</strong> ${paidTenants}</p>
                    <p><strong>المستأجرين غير المدفوعين:</strong> ${unpaidTenants}</p>
                </div>

                <div class="report-summary">
                    <div class="summary-item income-item">
                        <span>إجمالي الإيرادات الشهرية:</span>
                        <span>${totalIncome.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item expense-item">
                        <span>إجمالي المصروفات الشهرية:</span>
                        <span>${totalExpenses.toLocaleString()} ج.م</span>
                    </div>
                    <div class="summary-item net-item">
                        <span>صافي الدخل الشهري:</span>
                        <span>${(totalIncome - totalExpenses).toLocaleString()} ج.م</span>
                    </div>
                </div>

                <table class="report-table">
                    <thead>
                        <tr>
                            <th>المستأجر</th>
                            <th>الإيرادات</th>
                            <th>المصروفات</th>
                            <th>صافي الدخل</th>
                            <th>حالة الدفع</th>
                            <th>التليفون</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tenantData.map(tenant => `
                            <tr>
                                <td>${tenant.name}</td>
                                <td class="income-item">${tenant.income.toLocaleString()} ج.م</td>
                                <td class="expense-item">${tenant.expenses.toLocaleString()} ج.م</td>
                                <td class="net-item">${tenant.net.toLocaleString()} ج.م</td>
                                <td><span class="status-${tenant.status}">${getStatusText(tenant.status)}</span></td>
                                <td>${tenant.phone || 'غير محدد'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            showReport(`تقرير العقار: ${property.name}`, reportContent);
        }

        function exportMonthlyReport() {
            const month = document.getElementById('reportMonth').value;
            const year = document.getElementById('reportYear').value;

            if (!month || !year) {
                showNotification('يرجى اختيار الشهر والسنة أولاً', 'error');
                return;
            }

            const monthNames = {
                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            };

            let totalIncome = 0;
            let totalExpenses = 0;
            let reportData = [];

            properties.forEach(property => {
                property.tenants.forEach(tenant => {
                    if (!tenant.isEmpty) {
                        const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                                     tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                        const expenses = tenant.monthlyExpenses || 0;

                        totalIncome += income;
                        totalExpenses += expenses;

                        reportData.push({
                            العقار: property.name,
                            المستأجر: tenant.name,
                            الإيرادات: income,
                            المصروفات: expenses,
                            صافي_الدخل: income - expenses,
                            حالة_الدفع: getStatusText(tenant.paymentStatus),
                            التليفون: tenant.phone || 'غير محدد'
                        });
                    }
                });
            });

            const reportSummary = {
                الشهر: monthNames[month],
                السنة: year,
                إجمالي_الإيرادات: totalIncome,
                إجمالي_المصروفات: totalExpenses,
                صافي_الدخل: totalIncome - totalExpenses,
                تاريخ_التقرير: new Date().toLocaleDateString('ar-EG')
            };

            const exportData = {
                ملخص_التقرير: reportSummary,
                تفاصيل_المستأجرين: reportData
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `تقرير-${monthNames[month]}-${year}-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification(`تم رفع تقرير ${monthNames[month]} ${year} بنجاح`, 'success');
        }

        function showReport(title, content) {
            document.getElementById('reportTitle').textContent = title;
            document.getElementById('reportContent').innerHTML = content;
            document.getElementById('reportDisplay').style.display = 'block';
            document.getElementById('reportDisplay').scrollIntoView({ behavior: 'smooth' });
        }

        function closeReport() {
            document.getElementById('reportDisplay').style.display = 'none';
        }

        // PDF Export Functions
        function exportMonthlyReportPDF() {
            const month = document.getElementById('reportMonth').value;
            const year = document.getElementById('reportYear').value;

            if (!month || !year) {
                showNotification('يرجى اختيار الشهر والسنة أولاً', 'error');
                return;
            }

            const monthNames = {
                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            };

            let totalIncome = 0;
            let totalExpenses = 0;
            let reportData = [];

            properties.forEach(property => {
                property.tenants.forEach(tenant => {
                    if (!tenant.isEmpty) {
                        const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                                     tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                        const expenses = tenant.monthlyExpenses || 0;

                        totalIncome += income;
                        totalExpenses += expenses;

                        reportData.push({
                            property: property.name,
                            tenant: tenant.name,
                            income: income,
                            expenses: expenses,
                            net: income - expenses,
                            status: getStatusText(tenant.paymentStatus)
                        });
                    }
                });
            });

            // Create PDF
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Set font for Arabic support
            doc.setFont('helvetica');

            // Title
            doc.setFontSize(20);
            doc.text(`Monthly Report - ${monthNames[month]} ${year}`, 105, 20, { align: 'center' });
            doc.text(`تقرير شهر ${monthNames[month]} ${year}`, 105, 30, { align: 'center' });

            // Summary
            doc.setFontSize(14);
            doc.text('Summary / الملخص:', 20, 50);
            doc.setFontSize(12);
            doc.text(`Total Income / إجمالي الإيرادات: ${totalIncome.toLocaleString()} ج.م`, 20, 60);
            doc.text(`Total Expenses / إجمالي المصروفات: ${totalExpenses.toLocaleString()} ج.م`, 20, 70);
            doc.text(`Net Income / صافي الدخل: ${(totalIncome - totalExpenses).toLocaleString()} ج.م`, 20, 80);

            // Table headers
            doc.setFontSize(10);
            let yPos = 100;
            doc.text('Property', 20, yPos);
            doc.text('Tenant', 60, yPos);
            doc.text('Income', 100, yPos);
            doc.text('Expenses', 130, yPos);
            doc.text('Net', 160, yPos);
            doc.text('Status', 180, yPos);

            // Table data
            yPos += 10;
            reportData.forEach((row, index) => {
                if (yPos > 270) {
                    doc.addPage();
                    yPos = 20;
                }

                doc.text(row.property.substring(0, 15), 20, yPos);
                doc.text(row.tenant.substring(0, 15), 60, yPos);
                doc.text(`${row.income.toLocaleString()}`, 100, yPos);
                doc.text(`${row.expenses.toLocaleString()}`, 130, yPos);
                doc.text(`${row.net.toLocaleString()}`, 160, yPos);
                doc.text(row.status, 180, yPos);

                yPos += 8;
            });

            // Footer
            doc.setFontSize(8);
            doc.text(`Generated on: ${new Date().toLocaleDateString('ar-EG')}`, 20, 290);
            doc.text('Real Estate Management System', 150, 290);

            // Save PDF
            doc.save(`تقرير-${monthNames[month]}-${year}.pdf`);
            showNotification(`تم تصدير تقرير ${monthNames[month]} ${year} بصيغة PDF`, 'success');
        }

        function exportTenantReportPDF() {
            const tenantId = document.getElementById('tenantSelect').value;
            const year = document.getElementById('tenantReportYear').value;

            if (!tenantId || !year) {
                showNotification('يرجى اختيار المستأجر والسنة', 'error');
                return;
            }

            let tenant = null;
            let property = null;

            properties.forEach(prop => {
                const foundTenant = prop.tenants.find(t => t.id === tenantId);
                if (foundTenant) {
                    tenant = foundTenant;
                    property = prop;
                }
            });

            if (!tenant) {
                showNotification('المستأجر غير موجود', 'error');
                return;
            }

            const monthlyData = [];
            for (let month = 1; month <= 12; month++) {
                const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                             tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                const expenses = tenant.monthlyExpenses || 0;

                monthlyData.push({
                    month: month,
                    income: income,
                    expenses: expenses,
                    net: income - expenses
                });
            }

            const totalYearIncome = monthlyData.reduce((sum, data) => sum + data.income, 0);
            const totalYearExpenses = monthlyData.reduce((sum, data) => sum + data.expenses, 0);

            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            // Create PDF
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Title
            doc.setFontSize(18);
            doc.text(`Annual Tenant Report ${year}`, 105, 20, { align: 'center' });
            doc.text(`التقرير السنوي للمستأجر ${year}`, 105, 30, { align: 'center' });

            // Tenant Info
            doc.setFontSize(12);
            doc.text(`Property / العقار: ${property.name}`, 20, 50);
            doc.text(`Tenant / المستأجر: ${tenant.name}`, 20, 60);
            doc.text(`Phone / التليفون: ${tenant.phone || 'غير محدد'}`, 20, 70);

            // Summary
            doc.setFontSize(14);
            doc.text('Annual Summary / الملخص السنوي:', 20, 90);
            doc.setFontSize(12);
            doc.text(`Total Income / إجمالي الإيرادات: ${totalYearIncome.toLocaleString()} ج.م`, 20, 100);
            doc.text(`Total Expenses / إجمالي المصروفات: ${totalYearExpenses.toLocaleString()} ج.م`, 20, 110);
            doc.text(`Net Income / صافي الدخل: ${(totalYearIncome - totalYearExpenses).toLocaleString()} ج.م`, 20, 120);

            // Monthly breakdown
            doc.setFontSize(10);
            let yPos = 140;
            doc.text('Month', 20, yPos);
            doc.text('Income', 80, yPos);
            doc.text('Expenses', 120, yPos);
            doc.text('Net', 160, yPos);

            yPos += 10;
            monthlyData.forEach((data, index) => {
                if (yPos > 270) {
                    doc.addPage();
                    yPos = 20;
                }

                doc.text(monthNames[index], 20, yPos);
                doc.text(`${data.income.toLocaleString()} ج.م`, 80, yPos);
                doc.text(`${data.expenses.toLocaleString()} ج.م`, 120, yPos);
                doc.text(`${data.net.toLocaleString()} ج.م`, 160, yPos);

                yPos += 8;
            });

            // Footer
            doc.setFontSize(8);
            doc.text(`Generated on: ${new Date().toLocaleDateString('ar-EG')}`, 20, 290);
            doc.text('Real Estate Management System', 150, 290);

            // Save PDF
            doc.save(`تقرير-${tenant.name}-${year}.pdf`);
            showNotification(`تم تصدير التقرير السنوي للمستأجر ${tenant.name}`, 'success');
        }

        function exportPropertyReportPDF() {
            const propertyId = document.getElementById('propertySelect').value;

            if (!propertyId) {
                showNotification('يرجى اختيار العقار', 'error');
                return;
            }

            const property = properties.find(p => p.id === propertyId);
            if (!property) {
                showNotification('العقار غير موجود', 'error');
                return;
            }

            let totalIncome = 0;
            let totalExpenses = 0;
            let paidTenants = 0;
            let unpaidTenants = 0;

            const tenantData = property.tenants.map(tenant => {
                if (!tenant.isEmpty) {
                    const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                                 tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                    const expenses = tenant.monthlyExpenses || 0;

                    totalIncome += income;
                    totalExpenses += expenses;

                    if (tenant.paymentStatus === 'paid') paidTenants++;
                    else if (tenant.paymentStatus === 'unpaid') unpaidTenants++;

                    return {
                        name: tenant.name,
                        income: income,
                        expenses: expenses,
                        net: income - expenses,
                        status: getStatusText(tenant.paymentStatus),
                        phone: tenant.phone
                    };
                }
                return null;
            }).filter(t => t !== null);

            // Create PDF
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Title
            doc.setFontSize(18);
            doc.text('Property Report', 105, 20, { align: 'center' });
            doc.text(`تقرير العقار: ${property.name}`, 105, 30, { align: 'center' });

            // Property Info
            doc.setFontSize(12);
            doc.text(`Property Name / اسم العقار: ${property.name}`, 20, 50);
            doc.text(`Total Tenants / عدد المستأجرين: ${property.tenantCount}`, 20, 60);
            doc.text(`Paid Tenants / المدفوعين: ${paidTenants}`, 20, 70);
            doc.text(`Unpaid Tenants / غير المدفوعين: ${unpaidTenants}`, 20, 80);

            // Summary
            doc.setFontSize(14);
            doc.text('Monthly Summary / الملخص الشهري:', 20, 100);
            doc.setFontSize(12);
            doc.text(`Total Income / إجمالي الإيرادات: ${totalIncome.toLocaleString()} ج.م`, 20, 110);
            doc.text(`Total Expenses / إجمالي المصروفات: ${totalExpenses.toLocaleString()} ج.م`, 20, 120);
            doc.text(`Net Income / صافي الدخل: ${(totalIncome - totalExpenses).toLocaleString()} ج.م`, 20, 130);

            // Tenants table
            doc.setFontSize(10);
            let yPos = 150;
            doc.text('Tenant', 20, yPos);
            doc.text('Income', 70, yPos);
            doc.text('Expenses', 100, yPos);
            doc.text('Net', 130, yPos);
            doc.text('Status', 160, yPos);

            yPos += 10;
            tenantData.forEach((tenant, index) => {
                if (yPos > 270) {
                    doc.addPage();
                    yPos = 20;
                }

                doc.text(tenant.name.substring(0, 20), 20, yPos);
                doc.text(`${tenant.income.toLocaleString()}`, 70, yPos);
                doc.text(`${tenant.expenses.toLocaleString()}`, 100, yPos);
                doc.text(`${tenant.net.toLocaleString()}`, 130, yPos);
                doc.text(tenant.status, 160, yPos);

                yPos += 8;
            });

            // Footer
            doc.setFontSize(8);
            doc.text(`Generated on: ${new Date().toLocaleDateString('ar-EG')}`, 20, 290);
            doc.text('Real Estate Management System', 150, 290);

            // Save PDF
            doc.save(`تقرير-عقار-${property.name}.pdf`);
            showNotification(`تم تصدير تقرير العقار ${property.name}`, 'success');
        }

        // Excel Export Functions
        function exportMonthlyReportExcel() {
            const month = document.getElementById('reportMonth').value;
            const year = document.getElementById('reportYear').value;

            if (!month || !year) {
                showNotification('يرجى اختيار الشهر والسنة أولاً', 'error');
                return;
            }

            const monthNames = {
                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            };

            let totalIncome = 0;
            let totalExpenses = 0;
            let reportData = [];

            properties.forEach(property => {
                property.tenants.forEach(tenant => {
                    if (!tenant.isEmpty) {
                        const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                                     tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                        const expenses = tenant.monthlyExpenses || 0;

                        totalIncome += income;
                        totalExpenses += expenses;

                        reportData.push({
                            'العقار': property.name,
                            'المستأجر': tenant.name,
                            'الإيرادات (ج.م)': income,
                            'المصروفات (ج.م)': expenses,
                            'صافي الدخل (ج.م)': income - expenses,
                            'حالة الدفع': getStatusText(tenant.paymentStatus),
                            'التليفون': tenant.phone || 'غير محدد'
                        });
                    }
                });
            });

            // Add summary row
            reportData.unshift({
                'العقار': 'الملخص',
                'المستأجر': `${monthNames[month]} ${year}`,
                'الإيرادات (ج.م)': totalIncome,
                'المصروفات (ج.م)': totalExpenses,
                'صافي الدخل (ج.م)': totalIncome - totalExpenses,
                'حالة الدفع': 'إجمالي',
                'التليفون': new Date().toLocaleDateString('ar-EG')
            });

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(reportData);

            // Set column widths
            ws['!cols'] = [
                { width: 20 }, // العقار
                { width: 20 }, // المستأجر
                { width: 15 }, // الإيرادات
                { width: 15 }, // المصروفات
                { width: 15 }, // صافي الدخل
                { width: 15 }, // حالة الدفع
                { width: 15 }  // التليفون
            ];

            XLSX.utils.book_append_sheet(wb, ws, 'التقرير الشهري');

            // Save Excel file
            XLSX.writeFile(wb, `تقرير-${monthNames[month]}-${year}.xlsx`);
            showNotification(`تم تصدير تقرير ${monthNames[month]} ${year} بصيغة Excel`, 'success');
        }

        function exportTenantReportExcel() {
            const tenantId = document.getElementById('tenantSelect').value;
            const year = document.getElementById('tenantReportYear').value;

            if (!tenantId || !year) {
                showNotification('يرجى اختيار المستأجر والسنة', 'error');
                return;
            }

            let tenant = null;
            let property = null;

            properties.forEach(prop => {
                const foundTenant = prop.tenants.find(t => t.id === tenantId);
                if (foundTenant) {
                    tenant = foundTenant;
                    property = prop;
                }
            });

            if (!tenant) {
                showNotification('المستأجر غير موجود', 'error');
                return;
            }

            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            const monthlyData = [];
            let totalYearIncome = 0;
            let totalYearExpenses = 0;

            for (let month = 0; month < 12; month++) {
                const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                             tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                const expenses = tenant.monthlyExpenses || 0;

                totalYearIncome += income;
                totalYearExpenses += expenses;

                monthlyData.push({
                    'الشهر': monthNames[month],
                    'الإيرادات (ج.م)': income,
                    'المصروفات (ج.م)': expenses,
                    'صافي الدخل (ج.م)': income - expenses
                });
            }

            // Add summary row
            monthlyData.unshift({
                'الشهر': 'الملخص السنوي',
                'الإيرادات (ج.م)': totalYearIncome,
                'المصروفات (ج.م)': totalYearExpenses,
                'صافي الدخل (ج.م)': totalYearIncome - totalYearExpenses
            });

            // Add tenant info
            monthlyData.unshift({
                'الشهر': 'معلومات المستأجر',
                'الإيرادات (ج.م)': `العقار: ${property.name}`,
                'المصروفات (ج.م)': `المستأجر: ${tenant.name}`,
                'صافي الدخل (ج.م)': `التليفون: ${tenant.phone || 'غير محدد'}`
            });

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(monthlyData);

            // Set column widths
            ws['!cols'] = [
                { width: 20 }, // الشهر
                { width: 20 }, // الإيرادات
                { width: 20 }, // المصروفات
                { width: 20 }  // صافي الدخل
            ];

            XLSX.utils.book_append_sheet(wb, ws, 'التقرير السنوي');

            // Save Excel file
            XLSX.writeFile(wb, `تقرير-${tenant.name}-${year}.xlsx`);
            showNotification(`تم تصدير التقرير السنوي للمستأجر ${tenant.name}`, 'success');
        }

        function exportPropertyReportExcel() {
            const propertyId = document.getElementById('propertySelect').value;

            if (!propertyId) {
                showNotification('يرجى اختيار العقار', 'error');
                return;
            }

            const property = properties.find(p => p.id === propertyId);
            if (!property) {
                showNotification('العقار غير موجود', 'error');
                return;
            }

            let totalIncome = 0;
            let totalExpenses = 0;
            let paidTenants = 0;
            let unpaidTenants = 0;

            const tenantData = [];

            property.tenants.forEach(tenant => {
                if (!tenant.isEmpty) {
                    const income = tenant.paymentStatus === 'paid' ? tenant.rentAmount :
                                 tenant.paymentStatus === 'partial' ? tenant.rentAmount * 0.5 : 0;
                    const expenses = tenant.monthlyExpenses || 0;

                    totalIncome += income;
                    totalExpenses += expenses;

                    if (tenant.paymentStatus === 'paid') paidTenants++;
                    else if (tenant.paymentStatus === 'unpaid') unpaidTenants++;

                    tenantData.push({
                        'المستأجر': tenant.name,
                        'الإيرادات (ج.م)': income,
                        'المصروفات (ج.م)': expenses,
                        'صافي الدخل (ج.م)': income - expenses,
                        'حالة الدفع': getStatusText(tenant.paymentStatus),
                        'التليفون': tenant.phone || 'غير محدد'
                    });
                }
            });

            // Add summary row
            tenantData.unshift({
                'المستأجر': 'الملخص الإجمالي',
                'الإيرادات (ج.م)': totalIncome,
                'المصروفات (ج.م)': totalExpenses,
                'صافي الدخل (ج.م)': totalIncome - totalExpenses,
                'حالة الدفع': `مدفوع: ${paidTenants} | غير مدفوع: ${unpaidTenants}`,
                'التليفون': new Date().toLocaleDateString('ar-EG')
            });

            // Add property info
            tenantData.unshift({
                'المستأجر': 'معلومات العقار',
                'الإيرادات (ج.م)': `اسم العقار: ${property.name}`,
                'المصروفات (ج.م)': `عدد المستأجرين: ${property.tenantCount}`,
                'صافي الدخل (ج.م)': `تاريخ الإضافة: ${new Date(property.createdAt).toLocaleDateString('ar-EG')}`,
                'حالة الدفع': 'تقرير العقار',
                'التليفون': ''
            });

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(tenantData);

            // Set column widths
            ws['!cols'] = [
                { width: 20 }, // المستأجر
                { width: 15 }, // الإيرادات
                { width: 15 }, // المصروفات
                { width: 15 }, // صافي الدخل
                { width: 15 }, // حالة الدفع
                { width: 15 }  // التليفون
            ];

            XLSX.utils.book_append_sheet(wb, ws, 'تقرير العقار');

            // Save Excel file
            XLSX.writeFile(wb, `تقرير-عقار-${property.name}.xlsx`);
            showNotification(`تم تصدير تقرير العقار ${property.name}`, 'success');
        }

        // Auto-save every 30 seconds
        setInterval(() => {
            if (properties.length > 0) {
                saveData();
            }
        }, 30000);
    </script>
</body>
</html>
