1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lawoffice.adart3akar"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <!-- الصلاحيات المطلوبة -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:9:5-80
15-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:9:22-77
16    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
16-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:10:5-88
16-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:10:22-85
17
18    <permission
18-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.lawoffice.adart3akar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.lawoffice.adart3akar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:12:5-52:19
25        android:allowBackup="true"
25-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:13:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:14:9-65
28        android:extractNativeLibs="true"
29        android:fullBackupContent="@xml/backup_rules"
29-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:15:9-54
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:16:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:17:9-41
32        android:networkSecurityConfig="@xml/network_security_config"
32-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:22:9-69
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:18:9-54
34        android:supportsRtl="true"
34-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:19:9-35
35        android:theme="@style/Theme.RealEstateManager"
35-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:20:9-55
36        android:usesCleartextTraffic="true" >
36-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:21:9-44
37
38        <!-- النشاط الرئيسي -->
39        <activity
39-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:26:9-37:20
40            android:name="com.lawoffice.adart3akar.MainActivity"
40-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:27:13-41
41            android:configChanges="orientation|screenSize|keyboardHidden"
41-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:30:13-74
42            android:exported="true"
42-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:28:13-36
43            android:screenOrientation="portrait"
43-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:29:13-49
44            android:windowSoftInputMode="adjustResize" >
44-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:31:13-55
45            <intent-filter>
45-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:33:13-36:29
46                <action android:name="android.intent.action.MAIN" />
46-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:34:17-69
46-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:34:25-66
47
48                <category android:name="android.intent.category.LAUNCHER" />
48-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:35:17-77
48-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:35:27-74
49            </intent-filter>
50        </activity>
51
52        <!-- File Provider للمشاركة -->
53        <provider
54            android:name="androidx.core.content.FileProvider"
54-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:43:13-62
55            android:authorities="com.lawoffice.adart3akar.fileprovider"
55-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:44:13-64
56            android:exported="false"
56-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:45:13-37
57            android:grantUriPermissions="true" >
57-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:46:13-47
58            <meta-data
58-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:47:13-49:54
59                android:name="android.support.FILE_PROVIDER_PATHS"
59-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:48:17-67
60                android:resource="@xml/file_paths" />
60-->C:\Users\<USER>\Desktop\New folder\app\src\main\AndroidManifest.xml:49:17-51
61        </provider>
62        <provider
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
63            android:name="androidx.startup.InitializationProvider"
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
64            android:authorities="com.lawoffice.adart3akar.androidx-startup"
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
65            android:exported="false" >
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
66            <meta-data
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.emoji2.text.EmojiCompatInitializer"
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
68                android:value="androidx.startup" />
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eefc95c541adda1444a74349370ce8b1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
70-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eefc95c541adda1444a74349370ce8b1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
71                android:value="androidx.startup" />
71-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eefc95c541adda1444a74349370ce8b1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
74                android:value="androidx.startup" />
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
75        </provider>
76
77        <uses-library
77-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
78            android:name="androidx.window.extensions"
78-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
79            android:required="false" />
79-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
80        <uses-library
80-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
81            android:name="androidx.window.sidecar"
81-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
82            android:required="false" />
82-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
83
84        <receiver
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
85            android:name="androidx.profileinstaller.ProfileInstallReceiver"
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
86            android:directBootAware="false"
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
87            android:enabled="true"
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
88            android:exported="true"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
89            android:permission="android.permission.DUMP" >
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
91                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
94                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
97                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
98            </intent-filter>
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
100                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
101            </intent-filter>
102        </receiver>
103    </application>
104
105</manifest>
