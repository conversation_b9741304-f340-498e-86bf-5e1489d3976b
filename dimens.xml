<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- <PERSON><PERSON> and Padding -->
    <dimen name="margin_tiny">4dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_xlarge">32dp</dimen>
    
    <dimen name="padding_tiny">4dp</dimen>
    <dimen name="padding_small">8dp</dimen>
    <dimen name="padding_medium">16dp</dimen>
    <dimen name="padding_large">24dp</dimen>
    <dimen name="padding_xlarge">32dp</dimen>
    
    <!-- Text Sizes -->
    <dimen name="text_size_tiny">10sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_normal">14sp</dimen>
    <dimen name="text_size_medium">16sp</dimen>
    <dimen name="text_size_large">18sp</dimen>
    <dimen name="text_size_xlarge">20sp</dimen>
    <dimen name="text_size_xxlarge">24sp</dimen>
    <dimen name="text_size_title">28sp</dimen>
    <dimen name="text_size_headline">32sp</dimen>
    
    <!-- Button Dimensions -->
    <dimen name="button_height">48dp</dimen>
    <dimen name="button_height_small">36dp</dimen>
    <dimen name="button_height_large">56dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>
    <dimen name="button_corner_radius_large">24dp</dimen>
    
    <!-- Card Dimensions -->
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="card_elevation_pressed">8dp</dimen>
    
    <!-- Icon Sizes -->
    <dimen name="icon_size_tiny">16dp</dimen>
    <dimen name="icon_size_small">20dp</dimen>
    <dimen name="icon_size_normal">24dp</dimen>
    <dimen name="icon_size_medium">32dp</dimen>
    <dimen name="icon_size_large">48dp</dimen>
    <dimen name="icon_size_xlarge">64dp</dimen>
    
    <!-- Layout Dimensions -->
    <dimen name="toolbar_height">56dp</dimen>
    <dimen name="bottom_navigation_height">56dp</dimen>
    <dimen name="fab_size">56dp</dimen>
    <dimen name="fab_size_mini">40dp</dimen>
    
    <!-- WebView Dimensions -->
    <dimen name="webview_margin">0dp</dimen>
    <dimen name="webview_padding">0dp</dimen>
    
    <!-- Progress Bar -->
    <dimen name="progress_bar_size">48dp</dimen>
    <dimen name="progress_bar_size_small">24dp</dimen>
    
    <!-- Divider -->
    <dimen name="divider_height">1dp</dimen>
    <dimen name="divider_margin">16dp</dimen>
    
    <!-- Border Width -->
    <dimen name="border_width_thin">1dp</dimen>
    <dimen name="border_width_normal">2dp</dimen>
    <dimen name="border_width_thick">4dp</dimen>
    
    <!-- Elevation -->
    <dimen name="elevation_low">2dp</dimen>
    <dimen name="elevation_medium">4dp</dimen>
    <dimen name="elevation_high">8dp</dimen>
    <dimen name="elevation_very_high">16dp</dimen>
    
    <!-- Minimum Touch Target -->
    <dimen name="min_touch_target">48dp</dimen>
    
    <!-- List Item Heights -->
    <dimen name="list_item_height_small">48dp</dimen>
    <dimen name="list_item_height_normal">56dp</dimen>
    <dimen name="list_item_height_large">72dp</dimen>
    
    <!-- Image Dimensions -->
    <dimen name="image_size_small">32dp</dimen>
    <dimen name="image_size_medium">48dp</dimen>
    <dimen name="image_size_large">64dp</dimen>
    <dimen name="image_size_xlarge">96dp</dimen>
    
    <!-- Spacing -->
    <dimen name="spacing_tiny">2dp</dimen>
    <dimen name="spacing_small">4dp</dimen>
    <dimen name="spacing_normal">8dp</dimen>
    <dimen name="spacing_medium">12dp</dimen>
    <dimen name="spacing_large">16dp</dimen>
    <dimen name="spacing_xlarge">24dp</dimen>
    
    <!-- Form Elements -->
    <dimen name="input_height">48dp</dimen>
    <dimen name="input_corner_radius">8dp</dimen>
    <dimen name="input_padding_horizontal">16dp</dimen>
    <dimen name="input_padding_vertical">12dp</dimen>
</resources>
