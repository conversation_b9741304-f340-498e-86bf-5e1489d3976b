package com.lawoffice.adart3akar;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Environment;
import android.content.ContentValues;
import android.provider.MediaStore;
import android.os.Build;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.Manifest;
import android.content.pm.PackageManager;
import java.io.OutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import java.io.FileOutputStream;
import java.io.File;

public class MainActivity extends AppCompatActivity {

    private WebView webView;
    private static final int PERMISSION_REQUEST_CODE = 1001;

    // متغيرات لحفظ الملفات
    private String pendingFileContent = null;
    private String pendingFileName = null;
    private String pendingMimeType = null;
    private boolean isPendingBinaryFile = false;

    // ActivityResultLauncher لاختيار مكان الحفظ
    private ActivityResultLauncher<Intent> saveFileLauncher;

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // تهيئة ActivityResultLauncher لحفظ الملفات
        initializeSaveFileLauncher();

        // طلب الصلاحيات
        requestPermissions();

        // تهيئة WebView
        webView = findViewById(R.id.webview);

        // إعدادات WebView
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setDatabaseEnabled(true);
        // webSettings.setAppCacheEnabled(true); // Deprecated in API 33
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

        // تمكين التكبير
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDisplayZoomControls(false);

        // إعداد WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (url.startsWith("http://") || url.startsWith("https://")) {
                    // فتح الروابط الخارجية في المتصفح
                    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    startActivity(intent);
                    return true;
                }
                return false;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                // إخفاء شاشة التحميل إذا كانت موجودة
            }
        });

        // إعداد WebChromeClient للتعامل مع JavaScript alerts
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public boolean onJsAlert(WebView view, String url, String message, android.webkit.JsResult result) {
                Toast.makeText(MainActivity.this, message, Toast.LENGTH_LONG).show();
                result.confirm();
                return true;
            }
        });

        // إضافة JavaScript Interface للتفاعل مع Android
        webView.addJavascriptInterface(new AndroidInterface(), "Android");

        // تحميل ملف HTML من assets
        webView.loadUrl("file:///android_asset/real-estate-manager.html");
    }

    private void initializeSaveFileLauncher() {
        saveFileLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                    Uri uri = result.getData().getData();
                    if (uri != null && pendingFileContent != null) {
                        try {
                            OutputStream outputStream = getContentResolver().openOutputStream(uri);
                            if (outputStream != null) {
                                if (isPendingBinaryFile) {
                                    // حفظ ملف ثنائي (PDF/Excel)
                                    byte[] data = android.util.Base64.decode(pendingFileContent, android.util.Base64.DEFAULT);
                                    outputStream.write(data);
                                } else {
                                    // حفظ ملف نصي (JSON)
                                    outputStream.write(pendingFileContent.getBytes());
                                }
                                outputStream.close();

                                runOnUiThread(() -> {
                                    Toast.makeText(this, "تم حفظ الملف بنجاح: " + pendingFileName, Toast.LENGTH_LONG).show();
                                });
                            }
                        } catch (IOException e) {
                            runOnUiThread(() -> {
                                Toast.makeText(this, "خطأ في حفظ الملف: " + e.getMessage(), Toast.LENGTH_LONG).show();
                            });
                        } finally {
                            // مسح البيانات المؤقتة
                            clearPendingFileData();
                        }
                    }
                } else {
                    // المستخدم ألغى العملية
                    runOnUiThread(() -> {
                        Toast.makeText(this, "تم إلغاء حفظ الملف", Toast.LENGTH_SHORT).show();
                    });
                    clearPendingFileData();
                }
            }
        );
    }

    private void clearPendingFileData() {
        pendingFileContent = null;
        pendingFileName = null;
        pendingMimeType = null;
        isPendingBinaryFile = false;
    }

    private void requestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE,
                                Manifest.permission.READ_EXTERNAL_STORAGE},
                    PERMISSION_REQUEST_CODE);
            }
        }
    }

    // JavaScript Interface للتفاعل مع Android
    public class AndroidInterface {
        @JavascriptInterface
        public void showToast(String message) {
            runOnUiThread(() -> Toast.makeText(MainActivity.this, message, Toast.LENGTH_SHORT).show());
        }

        @JavascriptInterface
        public void shareData(String data) {
            runOnUiThread(() -> {
                Intent shareIntent = new Intent(Intent.ACTION_SEND);
                shareIntent.setType("text/plain");
                shareIntent.putExtra(Intent.EXTRA_TEXT, data);
                startActivity(Intent.createChooser(shareIntent, "مشاركة البيانات"));
            });
        }

        @JavascriptInterface
        public String getDeviceInfo() {
            return android.os.Build.MODEL + " - " + android.os.Build.VERSION.RELEASE;
        }

        @JavascriptInterface
        public void saveFile(String content, String fileName, String mimeType) {
            runOnUiThread(() -> {
                // حفظ البيانات مؤقتاً
                pendingFileContent = content;
                pendingFileName = fileName;
                pendingMimeType = mimeType;
                isPendingBinaryFile = false;

                // فتح نافذة اختيار مكان الحفظ
                openSaveFileDialog();
            });
        }

        @JavascriptInterface
        public void saveBinaryFile(String base64Data, String fileName, String mimeType) {
            runOnUiThread(() -> {
                // حفظ البيانات مؤقتاً
                pendingFileContent = base64Data;
                pendingFileName = fileName;
                pendingMimeType = mimeType;
                isPendingBinaryFile = true;

                // فتح نافذة اختيار مكان الحفظ
                openSaveFileDialog();
            });
        }

        @JavascriptInterface
        public void shareFile(String content, String fileName, String mimeType) {
            runOnUiThread(() -> {
                Intent shareIntent = new Intent(Intent.ACTION_SEND);
                shareIntent.setType(mimeType);
                shareIntent.putExtra(Intent.EXTRA_TEXT, content);
                shareIntent.putExtra(Intent.EXTRA_SUBJECT, fileName);
                startActivity(Intent.createChooser(shareIntent, "مشاركة " + fileName));
            });
        }

        @JavascriptInterface
        public void saveFileDirectly(String content, String fileName, String mimeType) {
            runOnUiThread(() -> {
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        // الحفظ المباشر في مجلد Downloads كخيار بديل
                        ContentValues values = new ContentValues();
                        values.put(MediaStore.MediaColumns.DISPLAY_NAME, fileName);
                        values.put(MediaStore.MediaColumns.MIME_TYPE, mimeType);
                        values.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS);

                        Uri uri = getContentResolver().insert(MediaStore.Downloads.EXTERNAL_CONTENT_URI, values);
                        if (uri != null) {
                            OutputStream outputStream = getContentResolver().openOutputStream(uri);
                            if (outputStream != null) {
                                outputStream.write(content.getBytes());
                                outputStream.close();
                                Toast.makeText(MainActivity.this, "تم حفظ الملف في مجلد التحميلات: " + fileName, Toast.LENGTH_LONG).show();
                            }
                        }
                    } else {
                        Toast.makeText(MainActivity.this, "يرجى استخدام زر المشاركة لحفظ الملف", Toast.LENGTH_LONG).show();
                    }
                } catch (IOException e) {
                    Toast.makeText(MainActivity.this, "خطأ في حفظ الملف: " + e.getMessage(), Toast.LENGTH_LONG).show();
                }
            });
        }
    }

    private void openSaveFileDialog() {
        try {
            Intent intent = new Intent(Intent.ACTION_CREATE_DOCUMENT);
            intent.addCategory(Intent.CATEGORY_OPENABLE);
            intent.setType(pendingMimeType);
            intent.putExtra(Intent.EXTRA_TITLE, pendingFileName);

            // إضافة أنواع الملفات المدعومة
            String[] mimeTypes = {pendingMimeType, "*/*"};
            intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes);

            saveFileLauncher.launch(intent);
        } catch (Exception e) {
            Toast.makeText(this, "خطأ في فتح نافذة الحفظ: " + e.getMessage(), Toast.LENGTH_LONG).show();
            clearPendingFileData();
        }
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }
}
