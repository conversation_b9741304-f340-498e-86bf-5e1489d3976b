<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <!-- Allow cleartext traffic for local development -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">***********</domain>
        
        <!-- Allow cleartext traffic for CDN resources -->
        <domain includeSubdomains="true">cdnjs.cloudflare.com</domain>
        <domain includeSubdomains="true">fonts.googleapis.com</domain>
        <domain includeSubdomains="true">fonts.gstatic.com</domain>
    </domain-config>
    
    <!-- Base configuration for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- Trust system certificate authorities -->
            <certificates src="system"/>
            <!-- Trust user-added certificate authorities -->
            <certificates src="user"/>
        </trust-anchors>
    </base-config>
    
    <!-- Debug configuration (only for debug builds) -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>
