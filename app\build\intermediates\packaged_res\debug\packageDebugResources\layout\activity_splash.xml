<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_background"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="32dp"
    tools:context=".SplashActivity">

    <!-- Logo -->
    <ImageView
        android:id="@+id/logo_image"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_marginBottom="24dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_building"
        android:tint="#ffffff" />

    <!-- App Title -->
    <TextView
        android:id="@+id/title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:text="@string/app_name"
        android:textColor="#ffffff"
        android:textSize="28sp"
        android:textStyle="bold" />

    <!-- App Subtitle -->
    <TextView
        android:id="@+id/subtitle_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:text="@string/app_description"
        android:textColor="#ffffff"
        android:textSize="16sp"
        android:alpha="0.9" />

    <!-- Loading Indicator -->
    <ProgressBar
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="24dp"
        android:indeterminateTint="#ffffff" />

    <!-- Loading Text -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="sans-serif"
        android:text="@string/loading_message"
        android:textColor="#ffffff"
        android:textSize="14sp"
        android:alpha="0.8" />

</LinearLayout>
