{"logs": [{"outputFile": "com.lawoffice.adart3akar.app-mergeReleaseResources-38:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1e2dd47e9ebc8ebee00aea44406d9542\\transformed\\preference-1.2.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,181,264,339,482,651,743", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "176,259,334,477,646,738,825"}, "to": {"startLines": "48,51,113,115,121,122,123", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4474,4675,9553,9708,10270,10439,10531", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "4545,4753,9623,9846,10434,10526,10613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69e8a0e0d964fb1e9e45715fb440dd3e\\transformed\\material-1.12.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,345,422,500,591,676,778,893,976,1037,1101,1190,1257,1317,1411,1475,1538,1594,1664,1731,1786,1905,1962,2026,2080,2153,2275,2358,2441,2534,2620,2705,2837,2915,2995,3117,3203,3287,3347,3399,3465,3535,3608,3679,3756,3828,3905,3977,4047,4160,4253,4326,4416,4509,4583,4655,4746,4800,4880,4946,5030,5115,5177,5241,5304,5370,5475,5580,5675,5776,5840,5896,5976,6061,6136", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,76,77,90,84,101,114,82,60,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79,84,74,75", "endOffsets": "264,340,417,495,586,671,773,888,971,1032,1096,1185,1252,1312,1406,1470,1533,1589,1659,1726,1781,1900,1957,2021,2075,2148,2270,2353,2436,2529,2615,2700,2832,2910,2990,3112,3198,3282,3342,3394,3460,3530,3603,3674,3751,3823,3900,3972,4042,4155,4248,4321,4411,4504,4578,4650,4741,4795,4875,4941,5025,5110,5172,5236,5299,5365,5470,5575,5670,5771,5835,5891,5971,6056,6131,6207"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3035,3111,3188,3266,3357,4174,4276,4391,4550,4611,4758,4847,4914,4974,5068,5132,5195,5251,5321,5388,5443,5562,5619,5683,5737,5810,5932,6015,6098,6191,6277,6362,6494,6572,6652,6774,6860,6944,7004,7056,7122,7192,7265,7336,7413,7485,7562,7634,7704,7817,7910,7983,8073,8166,8240,8312,8403,8457,8537,8603,8687,8772,8834,8898,8961,9027,9132,9237,9332,9433,9497,9628,9933,10018,10093", "endLines": "5,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "endColumns": "12,75,76,77,90,84,101,114,82,60,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79,84,74,75", "endOffsets": "314,3106,3183,3261,3352,3437,4271,4386,4469,4606,4670,4842,4909,4969,5063,5127,5190,5246,5316,5383,5438,5557,5614,5678,5732,5805,5927,6010,6093,6186,6272,6357,6489,6567,6647,6769,6855,6939,6999,7051,7117,7187,7260,7331,7408,7480,7557,7629,7699,7812,7905,7978,8068,8161,8235,8307,8398,8452,8532,8598,8682,8767,8829,8893,8956,9022,9127,9232,9327,9428,9492,9548,9703,10013,10088,10164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\412d4fb45e796eeac261181f9a418e93\\transformed\\appcompat-1.7.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,435,542,649,732,837,953,1043,1129,1220,1313,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2362,2460,2570,2670,2777,2936,9851", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "430,537,644,727,832,948,1038,1124,1215,1308,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2357,2455,2565,2665,2772,2931,3030,9928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\476525ada1d35df1ee329f5bd94fbe69\\transformed\\core-1.13.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3442,3544,3647,3752,3857,3956,4060,10169", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3539,3642,3747,3852,3951,4055,4169,10265"}}]}]}