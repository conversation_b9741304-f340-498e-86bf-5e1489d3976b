{"logs": [{"outputFile": "com.lawoffice.adart3akar.app-mergeReleaseResources-38:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69e8a0e0d964fb1e9e45715fb440dd3e\\transformed\\material-1.12.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,276,365,455,541,639,726,830,946,1037,1098,1164,1258,1325,1387,1480,1544,1612,1675,1749,1814,1868,1989,2046,2108,2162,2241,2369,2457,2538,2636,2719,2811,2956,3036,3118,3243,3331,3413,3473,3525,3591,3666,3744,3815,3894,3967,4043,4124,4193,4313,4418,4495,4586,4679,4753,4830,4922,4979,5060,5126,5210,5296,5359,5424,5488,5557,5667,5775,5874,5980,6044,6100,6183,6280,6358", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,89,85,97,86,103,115,90,60,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,80,97,82,91,144,79,81,124,87,81,59,51,65,74,77,70,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82,96,77,73", "endOffsets": "271,360,450,536,634,721,825,941,1032,1093,1159,1253,1320,1382,1475,1539,1607,1670,1744,1809,1863,1984,2041,2103,2157,2236,2364,2452,2533,2631,2714,2806,2951,3031,3113,3238,3326,3408,3468,3520,3586,3661,3739,3810,3889,3962,4038,4119,4188,4308,4413,4490,4581,4674,4748,4825,4917,4974,5055,5121,5205,5291,5354,5419,5483,5552,5662,5770,5869,5975,6039,6095,6178,6275,6353,6427"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3080,3169,3259,3345,3443,4280,4384,4500,4664,4725,4890,4984,5051,5113,5206,5270,5338,5401,5475,5540,5594,5715,5772,5834,5888,5967,6095,6183,6264,6362,6445,6537,6682,6762,6844,6969,7057,7139,7199,7251,7317,7392,7470,7541,7620,7693,7769,7850,7919,8039,8144,8221,8312,8405,8479,8556,8648,8705,8786,8852,8936,9022,9085,9150,9214,9283,9393,9501,9600,9706,9770,9905,10217,10314,10392", "endLines": "5,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "endColumns": "12,88,89,85,97,86,103,115,90,60,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,80,97,82,91,144,79,81,124,87,81,59,51,65,74,77,70,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82,96,77,73", "endOffsets": "321,3164,3254,3340,3438,3525,4379,4495,4586,4720,4786,4979,5046,5108,5201,5265,5333,5396,5470,5535,5589,5710,5767,5829,5883,5962,6090,6178,6259,6357,6440,6532,6677,6757,6839,6964,7052,7134,7194,7246,7312,7387,7465,7536,7615,7688,7764,7845,7914,8034,8139,8216,8307,8400,8474,8551,8643,8700,8781,8847,8931,9017,9080,9145,9209,9278,9388,9496,9595,9701,9765,9821,9983,10309,10387,10461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1e2dd47e9ebc8ebee00aea44406d9542\\transformed\\preference-1.2.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,277,356,502,671,758", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "173,272,351,497,666,753,837"}, "to": {"startLines": "48,51,113,115,121,122,123", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4591,4791,9826,9988,10567,10736,10823", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "4659,4885,9900,10129,10731,10818,10902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\476525ada1d35df1ee329f5bd94fbe69\\transformed\\core-1.13.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "38,39,40,41,42,43,44,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3530,3632,3740,3842,3943,4049,4156,10466", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3627,3735,3837,3938,4044,4151,4275,10562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\412d4fb45e796eeac261181f9a418e93\\transformed\\appcompat-1.7.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,443,555,668,758,863,982,1060,1136,1227,1320,1415,1509,1609,1702,1797,1892,1983,2074,2163,2277,2381,2480,2595,2700,2815,2977,10134", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "438,550,663,753,858,977,1055,1131,1222,1315,1410,1504,1604,1697,1792,1887,1978,2069,2158,2272,2376,2475,2590,2695,2810,2972,3075,10212"}}]}]}