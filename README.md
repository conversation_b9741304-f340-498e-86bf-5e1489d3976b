# نظام إدارة العقارات

## نظرة عامة
نظام إدارة العقارات هو تطبيق ويب شامل مصمم لإدارة العقارات والمستأجرين والعقود والمدفوعات بطريقة احترافية وسهلة الاستخدام.

## الميزات الرئيسية

### 🏠 إدارة العقارات
- إضافة وتعديل وحذف العقارات
- تصنيف العقارات (شقق، فلل، مكاتب، محلات تجارية)
- تتبع حالة العقار (متاح، مؤجر، تحت الصيانة)
- عرض تفاصيل شاملة لكل عقار
- البحث والفلترة المتقدمة

### 👥 إدارة المستأجرين
- قاعدة بيانات شاملة للمستأجرين
- معلومات الاتصال والهوية
- تتبع تاريخ الإيجار
- ملاحظات خاصة لكل مستأجر

### 📋 إدارة العقود
- إنشاء عقود إيجار جديدة
- تتبع تواريخ بداية وانتهاء العقود
- إدارة قيم الإيجار والتأمينات
- حالة العقود (نشط، منتهي، ملغي)

### 💰 إدارة المدفوعات
- تسجيل جميع أنواع المدفوعات
- تتبع طرق الدفع المختلفة
- تقارير مالية شاملة
- حالة المدفوعات (مدفوع، معلق، متأخر)

### 📊 لوحة التحكم والتقارير
- إحصائيات شاملة في الوقت الفعلي
- تتبع الأنشطة الأخيرة
- إجراءات سريعة
- تقارير مالية ورسوم بيانية

## التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق
- **JavaScript ES6+**: الوظائف والتفاعل
- **Local Storage**: حفظ البيانات محلياً
- **Font Awesome**: الأيقونات
- **Google Fonts**: خط Cairo العربي

## كيفية الاستخدام

### 1. فتح البرنامج
- افتح ملف `index.html` في أي متصفح حديث
- سيتم تحميل البيانات التجريبية تلقائياً في المرة الأولى

### 2. التنقل بين الأقسام
استخدم شريط التنقل العلوي للانتقال بين:
- **لوحة التحكم**: عرض الإحصائيات والأنشطة
- **العقارات**: إدارة العقارات
- **المستأجرين**: إدارة المستأجرين
- **العقود**: إدارة عقود الإيجار
- **المدفوعات**: تتبع المدفوعات
- **التقارير**: عرض التقارير والإحصائيات

### 3. إضافة عقار جديد
1. انتقل إلى قسم "العقارات"
2. اضغط على "إضافة عقار جديد"
3. املأ جميع البيانات المطلوبة
4. اضغط "حفظ العقار"

### 4. إضافة مستأجر جديد
1. انتقل إلى قسم "المستأجرين"
2. اضغط على "إضافة مستأجر جديد"
3. أدخل بيانات المستأجر
4. اضغط "حفظ المستأجر"

### 5. إنشاء عقد إيجار
1. انتقل إلى قسم "العقود"
2. اضغط على "عقد جديد"
3. اختر العقار والمستأجر
4. حدد تفاصيل العقد
5. اضغط "إنشاء العقد"

### 6. تسجيل دفعة
1. انتقل إلى قسم "المدفوعات"
2. اضغط على "تسجيل دفعة جديدة"
3. اختر العقار والمستأجر
4. أدخل تفاصيل الدفعة
5. اضغط "تسجيل الدفعة"

## الميزات المتقدمة

### البحث والفلترة
- استخدم مربع البحث للعثور على العقارات بسرعة
- استخدم الفلاتر لعرض العقارات حسب الحالة

### الإجراءات السريعة
- استخدم الإجراءات السريعة في لوحة التحكم للوصول المباشر للوظائف الأساسية

### التصدير والطباعة
- يمكن طباعة التقارير مباشرة من المتصفح
- التصميم محسن للطباعة

## حفظ البيانات
- جميع البيانات محفوظة محلياً في متصفحك
- لا تحتاج إلى اتصال بالإنترنت للاستخدام
- البيانات آمنة ومحفوظة تلقائياً

## المتطلبات
- متصفح حديث يدعم HTML5 و CSS3 و JavaScript
- لا يتطلب خادم أو قاعدة بيانات خارجية

## الدعم والتطوير
هذا البرنامج قابل للتخصيص والتطوير حسب احتياجاتك الخاصة.

## الملفات المضمنة
- `index.html` - الصفحة الرئيسية
- `styles.css` - ملف التصميم
- `script.js` - الوظائف الرئيسية
- `data.js` - إدارة البيانات
- `README.md` - دليل الاستخدام

---

## ✅ آخر التحديثات - إصلاح مشاكل حالة الدفع وتاريخ الدفع

### 🎯 **المشاكل التي تم حلها:**

#### ❌ **المشكلة الأولى: حالة الدفع تظهر بالإنجليزية**
- **قبل الإصلاح:** "paid"
- **بعد الإصلاح:** "مدفوع" ✅

#### ❌ **المشكلة الثانية: تاريخ الدفع يظهر "undefined"**
- **قبل الإصلاح:** "undefined"
- **بعد الإصلاح:** "١٥/٠٦/٢٠٢٤" ✅

### 🔧 **الإصلاحات المنجزة:**

#### **1. إصلاح التقرير الشهري:**
- ✅ استخدام المنطق الصحيح للبحث في سجل المدفوعات
- ✅ دعم البيانات القديمة والجديدة
- ✅ عرض حالة الدفع بالعربية
- ✅ عرض تاريخ الدفع بالتنسيق العربي

#### **2. إصلاح تقارير PDF و Excel:**
- ✅ نفس المنطق المحسن للبحث في المدفوعات
- ✅ عرض صحيح للحالة والتاريخ
- ✅ تنسيق عربي موحد

#### **3. تنظيف الكود:**
- ✅ حذف الدوال غير المستخدمة
- ✅ إزالة الكود المكرر
- ✅ توحيد منطق عرض الحالة

### 📊 **النتائج النهائية:**

| العقار | المستأجر | الإيرادات | المصروفات | صافي الدخل | حالة الدفع | تاريخ الدفع | عدد الدفعات |
|---------|----------|-----------|------------|------------|------------|-------------|-------------|
| المجمع | أحمد | 1,300 ج.م | 0 ج.م | 1,300 ج.م | **مدفوع** | **١٥/٠٦/٢٠٢٤** | **1** |

### ✅ **قائمة التحقق:**

- [x] حالة الدفع تظهر بالعربية
- [x] تاريخ الدفع يظهر بالتنسيق الصحيح
- [x] التقارير الشهرية تعمل بدقة
- [x] تقارير PDF محدثة
- [x] تقارير Excel محدثة
- [x] الإحصائيات دقيقة
- [x] دعم البيانات القديمة والجديدة
- [x] تنظيف الكود وحذف المكرر

**البرنامج الآن يعمل بشكل مثالي مع عرض صحيح لحالة الدفع وتاريخ الدفع!** 🎉✨

---

**تم تطوير هذا البرنامج بعناية فائقة لضمان سهولة الاستخدام والأداء العالي**

---

## 🆕 آخر التحديثات - نظام المصروفات المحسن

### 🎯 **التحسينات الجديدة:**

#### ✅ **نظام المصروفات المتقدم:**
- **إضافة مصروف:** إضافة مصروفات مع تاريخ وتصنيف
- **سجل المصروفات:** تتبع جميع المصروفات لكل مستأجر
- **أنواع المصروفات:** صيانة، فواتير، تنظيف، إصلاحات، تأمين، ضرائب، أخرى
- **ملاحظات:** إضافة ملاحظات لكل مصروف

#### ✅ **ضبط المصروفات الشهرية:**
- **مصروفات حسب التاريخ:** المصروفات تُحسب حسب الشهر الفعلي
- **لا تكرار:** إزالة نظام المصروفات الشهرية الثابتة
- **دقة في التقارير:** التقارير تعرض المصروفات الفعلية للشهر المحدد

#### ✅ **تحسينات الواجهة:**
- **أزرار جديدة:** إضافة مصروف + سجل المصروفات
- **عرض محسن:** عدد المدفوعات وعدد المصروفات
- **تصميم موحد:** نفس تصميم نظام المدفوعات

### 📊 **مثال عملي:**

#### **إضافة مصروف:**
- **التاريخ:** 15/06/2024
- **المبلغ:** 500 ج.م
- **النوع:** صيانة
- **الملاحظات:** إصلاح تسريب في الحمام

#### **النتيجة في التقرير:**
| العقار | المستأجر | الإيرادات | المصروفات | صافي الدخل |
|---------|----------|-----------|------------|------------|
| المجمع | أحمد | 1,300 ج.م | **500 ج.م** | **800 ج.م** |

### 🎯 **الفوائد الجديدة:**

#### **1. دقة في المحاسبة:**
- المصروفات تُحسب حسب التاريخ الفعلي
- لا تكرار للمصروفات في كل شهر
- تتبع دقيق للمصروفات الفعلية

#### **2. تصنيف المصروفات:**
- تصنيف المصروفات حسب النوع
- سهولة في التتبع والمراجعة
- تقارير مفصلة حسب نوع المصروف

#### **3. مرونة في الإدخال:**
- إضافة مصروفات في أي وقت
- تعديل وحذف المصروفات
- ملاحظات مفصلة لكل مصروف

### ✅ **قائمة التحقق الجديدة:**

- [x] نظام مصروفات متقدم
- [x] سجل مصروفات لكل مستأجر
- [x] أنواع مصروفات متعددة
- [x] حساب مصروفات شهرية دقيقة
- [x] إزالة المصروفات الشهرية الثابتة
- [x] تحديث جميع التقارير
- [x] واجهة محسنة
- [x] تنظيف الكود

### 🚀 **كيفية الاستخدام الجديدة:**

#### **إضافة مصروف:**
1. اضغط على "إضافة مصروف" بجانب المستأجر
2. أدخل مبلغ المصروف وتاريخه
3. اختر نوع المصروف
4. أضف ملاحظات (اختياري)
5. احفظ المصروف

#### **عرض سجل المصروفات:**
1. اضغط على "سجل المصروفات"
2. اعرض جميع المصروفات للمستأجر
3. احذف أي مصروف غير صحيح
4. راجع التفاصيل والملاحظات

**النظام الآن يوفر إدارة شاملة ودقيقة للمدفوعات والمصروفات مع تتبع زمني دقيق!** 🎉💰📊
