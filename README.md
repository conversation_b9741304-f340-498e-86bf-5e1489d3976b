# 🏠 نظام إدارة العقارات - Real Estate Management System

## 📱 تطبيق Android APK جاهز للاستخدام

نظام شامل لإدارة العقارات والمستأجرين تم تحويله إلى تطبيق Android احترافي.

## 📦 الملفات الجاهزة

### **ملفات APK:**
- **real-estate-manager.apk** (6.3 MB) - نسخة التطوير للاختبار
- **real-estate-manager-release.apk** (5.1 MB) - نسخة الإنتاج للتوزيع

### **مشروع Android Studio:**
- مجلد `app/` - يحتوي على المشروع الكامل
- جميع الملفات منظمة في أماكنها الصحيحة
- جاهز للفتح في Android Studio

## 🚀 كيفية التثبيت

### **على الهاتف:**
1. ا<PERSON><PERSON><PERSON> ملف `real-estate-manager.apk` إلى الهاتف
2. فعّل "تثبيت من مصادر غير معروفة" في الإعدادات
3. اضغط على ملف APK لتثبيته
4. افتح التطبيق واستمتع!

### **للتطوير:**
1. افتح Android Studio
2. اختر "Open an existing project"
3. اختر مجلد المشروع
4. انتظر تحميل المشروع
5. اضغط "Run" لتشغيل التطبيق

## ✨ الميزات الرئيسية

### 🏢 **إدارة العقارات:**
- إضافة وتعديل وحذف العقارات
- إدارة متعددة المستأجرين لكل عقار
- تتبع تفاصيل كل عقار والمستأجرين

### 💰 **نظام المدفوعات المتقدم:**
- سجل مدفوعات شامل لكل مستأجر
- مدفوعات متعددة في الشهر الواحد
- تواريخ دقيقة وملاحظات تفصيلية

### 💸 **نظام المصروفات المحسن:**
- سجل مصروفات تفصيلي مع تواريخ
- تصنيف المصروفات (صيانة، فواتير، تنظيف، إلخ)
- حساب شهري دقيق بناءً على التواريخ الفعلية

### 📊 **التقارير والإحصائيات:**
- تقارير شهرية وسنوية دقيقة
- تصدير Excel و PDF
- إحصائيات شاملة ومحدثة

### 📱 **ميزات Android:**
- واجهة متجاوبة للهواتف
- دعم اللغة العربية (RTL)
- مشاركة البيانات عبر النظام
- حفظ محلي للبيانات

## 🔧 المواصفات التقنية

### **متطلبات النظام:**
- **Android:** 5.0+ (API 21)
- **RAM:** 1GB+
- **Storage:** 50MB+

### **الميزات المدمجة:**
- WebView محسن مع دعم JavaScript كامل
- Local Storage لحفظ البيانات
- Android Interface للتفاعل مع النظام
- Toast Messages للإشعارات

## 📊 إحصائيات المشروع

### **الملفات:**
- **2 ملف APK** جاهز للاستخدام
- **مشروع Android** كامل ومنظم
- **كود محسن** وآمن

### **الحجم:**
- **Debug APK:** 6.3 MB
- **Release APK:** 5.1 MB
- **مشروع كامل:** منظم ومبسط

## 🎯 الاستخدام

### **للمستخدمين العاديين:**
- استخدم ملف `real-estate-manager.apk`
- ثبته على الهاتف مباشرة
- ابدأ في إدارة عقاراتك

### **للمطورين:**
- استخدم مجلد `app/` في Android Studio
- طور وحسن التطبيق حسب احتياجاتك
- ابني APK جديد عند الحاجة

## 🔄 بناء APK جديد

إذا أردت بناء APK جديد:

```bash
# للتطوير
./gradlew assembleDebug

# للإنتاج
./gradlew assembleRelease
```

الملفات ستكون في:
- `app/build/outputs/apk/debug/`
- `app/build/outputs/apk/release/`

## 📞 الدعم

### **المشاكل الشائعة:**
- **لا يثبت:** تأكد من تفعيل "مصادر غير معروفة"
- **لا يعمل:** تأكد من إصدار Android 5.0+
- **بطء:** أعد تشغيل التطبيق

### **للمطورين:**
- تأكد من تثبيت Android Studio
- استخدم SDK 21+ للتوافق
- راجع ملفات المشروع للتفاصيل

---

**تطبيق نظام إدارة العقارات جاهز للاستخدام على Android!** 🎉📱💰🏠
