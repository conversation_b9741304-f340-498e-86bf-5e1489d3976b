<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_background"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/padding_xlarge"
    tools:context=".SplashActivity">

    <!-- Logo -->
    <ImageView
        android:id="@+id/logo_image"
        android:layout_width="@dimen/icon_size_xlarge"
        android:layout_height="@dimen/icon_size_xlarge"
        android:layout_marginBottom="@dimen/margin_large"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_building"
        android:tint="@color/text_on_primary" />

    <!-- App Title -->
    <TextView
        android:id="@+id/title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/margin_medium"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:text="@string/app_name"
        android:textColor="@color/text_on_primary"
        android:textSize="@dimen/text_size_title"
        android:textStyle="bold" />

    <!-- App Subtitle -->
    <TextView
        android:id="@+id/subtitle_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/margin_xlarge"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:text="@string/app_description"
        android:textColor="@color/text_on_primary"
        android:textSize="@dimen/text_size_medium"
        android:alpha="0.9" />

    <!-- Loading Indicator -->
    <ProgressBar
        android:layout_width="@dimen/progress_bar_size"
        android:layout_height="@dimen/progress_bar_size"
        android:layout_marginTop="@dimen/margin_large"
        android:indeterminateTint="@color/text_on_primary" />

    <!-- Loading Text -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_medium"
        android:fontFamily="sans-serif"
        android:text="@string/loading_message"
        android:textColor="@color/text_on_primary"
        android:textSize="@dimen/text_size_normal"
        android:alpha="0.8" />

    <!-- Version Info -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_xlarge"
        android:fontFamily="sans-serif"
        android:text="@string/version"
        android:textColor="@color/text_on_primary"
        android:textSize="@dimen/text_size_small"
        android:alpha="0.7" />

</LinearLayout>
