<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">

    <!-- Main building complex -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M25,80 L25,40 L54,15 L83,40 L83,80 Z" />

    <!-- Building shadow/depth -->
    <path
        android:fillColor="#E8E8E8"
        android:pathData="M83,40 L83,80 L85,82 L85,42 Z" />

    <!-- Roof with gradient -->
    <path android:pathData="M20,40 L54,10 L88,40 L83,40 L54,15 L25,40 Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="88"
                android:endY="40"
                android:startX="20"
                android:startY="10"
                android:type="linear">
                <item
                    android:color="#FFD700"
                    android:offset="0.0" />
                <item
                    android:color="#FFA500"
                    android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>

    <!-- Building windows - Floor 1 -->
    <path
        android:fillColor="#4A90E2"
        android:pathData="M32,45 L38,45 L38,52 L32,52 Z" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M42,45 L48,45 L48,52 L42,52 Z" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M60,45 L66,45 L66,52 L60,52 Z" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M70,45 L76,45 L76,52 L70,52 Z" />

    <!-- Building windows - Floor 2 -->
    <path
        android:fillColor="#4A90E2"
        android:pathData="M32,56 L38,56 L38,63 L32,63 Z" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M42,56 L48,56 L48,63 L42,63 Z" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M60,56 L66,56 L66,63 L60,63 Z" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M70,56 L76,56 L76,63 L70,63 Z" />

    <!-- Building windows - Floor 3 -->
    <path
        android:fillColor="#4A90E2"
        android:pathData="M32,67 L38,67 L38,74 L32,74 Z" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M42,67 L48,67 L48,74 L42,74 Z" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M60,67 L66,67 L66,74 L60,74 Z" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M70,67 L76,67 L76,74 L70,74 Z" />

    <!-- Main entrance door -->
    <path android:pathData="M50,63 L58,63 L58,80 L50,80 Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="58"
                android:endY="80"
                android:startX="50"
                android:startY="63"
                android:type="linear">
                <item
                    android:color="#8B4513"
                    android:offset="0.0" />
                <item
                    android:color="#654321"
                    android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>

    <!-- Door handle -->
    <path
        android:fillColor="#FFD700"
        android:pathData="M55,70 L57,70 L57,72 L55,72 Z" />

    <!-- Door window -->
    <path
        android:fillColor="#4A90E2"
        android:pathData="M51,65 L57,65 L57,68 L51,68 Z" />

    <!-- Money symbol (top right) -->
    <path android:pathData="M85,15 L95,15 L95,25 L85,25 Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="95"
                android:endY="25"
                android:startX="85"
                android:startY="15"
                android:type="radial"
                android:gradientRadius="7">
                <item
                    android:color="#FFD700"
                    android:offset="0.0" />
                <item
                    android:color="#FFA500"
                    android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M87,17 L93,17 L93,18 L90,18 L90,22 L93,22 L93,23 L87,23 L87,22 L90,22 L90,18 L87,18 Z" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M89,16 L91,16 L91,24 L89,24 Z" />

    <!-- Key symbol (top left) -->
    <path android:pathData="M13,15 L23,15 L23,25 L13,25 Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="23"
                android:endY="25"
                android:startX="13"
                android:startY="15"
                android:type="radial"
                android:gradientRadius="7">
                <item
                    android:color="#FFD700"
                    android:offset="0.0" />
                <item
                    android:color="#FFA500"
                    android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M15,17 L21,17 L21,23 L15,23 Z" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M16,18 L20,18 L20,22 L16,22 Z" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M21,19 L23,19 L23,21 L21,21 Z" />

    <!-- Company name indicator -->
    <path
        android:fillColor="#4A90E2"
        android:pathData="M30,85 L78,85 L78,88 L30,88 Z" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M32,86 L76,86 L76,87 L32,87 Z" />

    <!-- Decorative elements -->
    <path
        android:fillColor="#FFD700"
        android:pathData="M54,8 L56,8 L56,12 L54,12 Z" />
    <path
        android:fillColor="#FFD700"
        android:pathData="M52,10 L58,10 L58,11 L52,11 Z" />

</vector>