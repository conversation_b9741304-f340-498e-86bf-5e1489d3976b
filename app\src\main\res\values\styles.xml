<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Base application theme -->
    <style name="Theme.RealEstateManager" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">#667eea</item>
        <item name="colorPrimaryVariant">#764ba2</item>
        <item name="colorOnPrimary">#ffffff</item>

        <!-- Secondary brand color -->
        <item name="colorSecondary">#03DAC5</item>
        <item name="colorSecondaryVariant">#018786</item>
        <item name="colorOnSecondary">#000000</item>

        <!-- Status bar color -->
        <item name="android:statusBarColor">#667eea</item>
        <item name="android:navigationBarColor">#667eea</item>

        <!-- Window background -->
        <item name="android:windowBackground">#f5f5f5</item>

        <!-- Text colors -->
        <item name="android:textColorPrimary">#333333</item>
        <item name="android:textColorSecondary">#666666</item>

        <!-- Support RTL -->
        <item name="android:layoutDirection">rtl</item>
        <item name="android:textDirection">rtl</item>
    </style>

    <!-- Splash screen theme -->
    <style name="SplashTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:statusBarColor">#667eea</item>
        <item name="android:navigationBarColor">#667eea</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <!-- WebView theme -->
    <style name="WebViewTheme" parent="Theme.RealEstateManager">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">false</item>
    </style>

    <!-- Button styles -->
    <style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="backgroundTint">#667eea</item>
        <item name="android:textColor">#ffffff</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">#667eea</item>
        <item name="android:textColor">#667eea</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <!-- Text styles -->
    <style name="TitleText">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">#333333</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="SubtitleText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">#666666</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="BodyText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">#333333</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

</resources>
